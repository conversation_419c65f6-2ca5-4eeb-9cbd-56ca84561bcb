<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Webkul\MpSellerCategory\Controller\Category;

use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\App\Action\Context;
use Magento\Ui\Component\MassAction\Filter;
use Webkul\MpSellerCategory\Helper\Data as HelperData;
use Webkul\MpSellerCategory\Model\ResourceModel\Category\CollectionFactory;

class MassDelete extends \Magento\Framework\App\Action\Action
{
    /**
     * @var Filter
     */
    protected $_filter;

    /**
     * @var HelperData
     */
    protected $_helper;

    /**
     * @var CollectionFactory
     */
    protected $_collectionFactory;

    /**
     * @param Context $context
     * @param Filter $filter
     * @param HelperData $helper
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(
        Context $context,
        Filter $filter,
        HelperData $helper,
        CollectionFactory $collectionFactory
    ) {
        $this->_filter = $filter;
        $this->_helper = $helper;
        $this->_collectionFactory = $collectionFactory;
        parent::__construct($context);
    }

    /**
     * Execute action.
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        try {
            $categoryIds = $this->getRequest()->getParam('category_mass_action');
            $sellerCategoryCollection = $this->_collectionFactory->create();
            if ($categoryIds) {
                $collection = $sellerCategoryCollection->addFieldToFilter('entity_id', ['in' => $categoryIds]);
            } else {
                $collection = $this->_filter->getCollection($sellerCategoryCollection);
            }
            $countRecord = $collection->getSize();
            $collection->removeCategories();
            $this->_helper->clearCache();
            $this->messageManager->addSuccess(
                __(
                    'A total of %1 record(s) have been deleted.',
                    $countRecord
                )
            );

            return $this->resultRedirectFactory->create()->setPath(
                'mpsellercategory/category/manage',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        } catch (\Exception $e) {
            $this->messageManager->addError($e->getMessage());

            return $this->resultRedirectFactory->create()->setPath(
                'mpsellercategory/category/manage',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }
    }
}
