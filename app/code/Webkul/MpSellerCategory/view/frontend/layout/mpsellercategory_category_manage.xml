<?xml version="1.0"?>
<!--
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
    <head>
        <css src="Webkul_MpSellerCategory::css/styles.css"/>
        <css src="Webkul_Marketplace::css/wk_block.css"/>
        <css src="Webkul_Marketplace::css/style.css"/>
        <css src="Webkul_Marketplace::css/product.css"/>
        <css src="Webkul_Marketplace::css/layout.css"/>
        <script src="Webkul_MpSellerCategory::js/category/list.js"/>
    </head>
    <body>
        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Manage Categories</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="content">
            <block class="Magento\Framework\View\Element\Template" name="mpsellercategory_category_manage" template="Webkul_MpSellerCategory::category/list.phtml" cacheable="false"></block>
        </referenceContainer>
        <referenceContainer name="mpsellercategory_category_manage">
            <uiComponent name="mpsellercategory_category_list_front"/>
        </referenceContainer>
    </body>
</page>
