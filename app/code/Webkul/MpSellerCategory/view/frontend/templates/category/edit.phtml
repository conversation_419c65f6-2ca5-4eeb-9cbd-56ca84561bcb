<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
?>
<?php
    $helper = $block->getMpHelper();
 
    $isPartner = $helper->isSeller();
    $backUrl = $block->getStoreUrl().'mpsellercategory/category/manage/';
    $data = ["products" => $block->getProductIds(true),
             "backUrl"=>$backUrl, ];
    $data = $block->getJsonFromArray($data);
    $categoryData = $block->getCategoryData();
    $id = $categoryData['id'];
    $categoryName = $categoryData['category_name'];
    $position = $categoryData['position'];
    $status = $categoryData['status'];
    $productIds = $categoryData['product_ids'];
    $allStatus = $block->getAllStatus();
    ?>
<div class="wk-mpsellercategory-container">
    <?php if ($isPartner == 1): ?>
        <form action="<?= $escaper->escapeUrl($block->getUrl('mpsellercategory/category/save')) ?>" 
        enctype="multipart/form-data" method="post" id="form-save-category" 
        data-mage-init='{"validation":{}}'>
            <div class="fieldset wk-ui-component-container">
                <?= $block->getBlockHtml('formkey') ?>
                <?= $block->getBlockHtml('seller.formkey') ?>
                <input type="hidden" name="id" value="<?= $escaper->escapeHtml($id) ?>">
                <input type="hidden" name="product_ids" id="product_ids"
                value="<?= $escaper->escapeHtml($productIds); ?>">
                <div class="page-main-actions">
                    <div class="page-actions-placeholder"></div>
                    <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
                        <div class="page-actions-inner" data-title="<?= $escaper->escapeHtml(__("Category")); ?>">
                            <div class="page-actions-buttons">
                                <button id="back" title="<?= $escaper->escapeHtml(__("Back")); ?>" type="button" 
                                class="action- scalable back wk-ui-grid-btn-back wk-ui-grid-btn" 
                                data-ui-id="back-button">
                                    <span><?= $escaper->escapeHtml(__("Back")); ?></span>
                                </button>
                                <button id="save"
                                title="<?= $escaper->escapeHtml(__("Save Category")); ?>" type="submit" 
                                class="action- scalable save primary ui-button ui-widget 
                                ui-state-default ui-corner-all ui-button-text-only wk-ui-grid-btn 
                                wk-ui-grid-btn-primary" 
                                data-form-role="save" 
                                data-ui-id="save-button" role="button" aria-disabled="false">
                                    <span class="ui-button-text">
                                        <span><?= $escaper->escapeHtml(__("Save Category")); ?></span>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="field required">
                    <label for="category_name" class="label">
                        <span><?= $escaper->escapeHtml(__("Name")); ?></span>
                    </label>
                    <div class="tooltip">
                     <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                     <span class="tooltiptext"><?= $escaper->escapeHtml(__('Update your category name')) ?></span>
                     </div>
                    <div class="control">
                        <input type="text" class="input-text required-entry" name="category_name" 
                        data-validate="{required:true}" title="<?= $escaper->escapeHtml(__("Name")); ?>" 
                        id="category_name" value="<?= $escaper->escapeHtml($block->escapeHtml($categoryName)) ?>">
                    </div>
                </div>
                <div class="field required">
                    <label for="position" class="label">
                        <span><?= $escaper->escapeHtml(__("Position")); ?></span>
                    </label>
                    <div class="tooltip">
                    <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Update your category position')) ?></span>
                    </div>
                    <div class="control">
                        <input type="text" class="input-text required-entry validate-digits" 
                        name="position" 
                        data-validate="{required:true}" title="<?= $escaper->escapeHtml(__("Position")); ?>" 
                        id="position" value="<?= $escaper->escapeHtml($block->escapeHtml($position)) ?>">
                    </div>
                </div>
                <div class="field required">
                    <label for="status" class="label">
                        <span><?= $escaper->escapeHtml(__("Status")); ?></span>
                    </label>
                    <div class="tooltip">
                    <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Update the current status of category'))?></span>
                    </div>
                    <div class="control">
                        <select data-validate="{'validate-select':true}"
                        title="<?= $escaper->escapeHtml(__("Status")); ?>" 
                        id="status" name="status" class="required-entry" data-ui-id="select-status">
                            <?php foreach ($allStatus as $value => $label): ?>
                                <?php if ($status == $value): ?>
                                    <option value="<?= $escaper->escapeHtml($value); ?>" 
                                    selected><?= $escaper->escapeHtml(__($label)); ?>
                                    </option>
                                <?php else: ?>
                                    <option 
                                    value="<?= $escaper->escapeHtml($value); ?>">
                                        <?= $escaper->escapeHtml(__($label)); ?>
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
        </form>
        <div class="wk-category-products">
            <div class="fieldset wk-ui-component-container">
                <div class="field">
                    <label for="products" class="label wk-head-title">
                        <span><?= $escaper->escapeHtml(__("Products in Category")); ?></span>
                    </label>
                    <div class="control">
                        <?= /* @noEscape */ $block->getChildHtml(); ?>
                    </div>
                </div>
            </div>
        </div>
        <script type="text/x-magento-init">
            {
                "*": {
                    "Webkul_MpSellerCategory/js/category/edit": <?= /* @noEscape */ $data ?>
                }
            }
        </script>
    <?php else: ?>
        <h2 class="wk-mp-error-msg">
            <?= $escaper->escapeHtml(__("To Become Seller Please Contact to Admin.")); ?>
        </h2>
    <?php endif; ?>
</div>
