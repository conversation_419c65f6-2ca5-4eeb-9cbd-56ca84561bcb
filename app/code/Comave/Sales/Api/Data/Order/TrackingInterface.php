<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Api\Data\Order;

use Magento\Framework\Api\ExtensibleDataInterface;

interface TrackingInterface extends ExtensibleDataInterface
{
    public const string TRACKING_ID = 'tracking_id';
    public const string ORDER_ID = 'order_id';
    public const string SELLER_ID = 'seller_id';
    public const string TRACKING_NUMBER = 'tracking_number';
    public const string TRACKING_STATUS = 'tracking_status';
    public const string DATE = 'date';
    public const string PAYLOAD = 'payload';

    /**
     * @param $id
     * @return self
     */
    public function setId($id);

    /**
     * @return int
     */
    public function getId();

    /**
     * @param int $id
     * @return self
     */
    public function setTrackingId(int $id): self;

    /**
     * @return int|null
     */
    public function getTrackingId(): ?int;

    /**
     * @param int $orderId
     * @return self
     */
    public function setOrderId(int $orderId): self;

    /**
     * Get Order ID
     *
     * @return int
     */
    public function getOrderId(): int;

    /**
     * @param int $sellerId
     * @return self
     */
    public function setSellerId(int $sellerId): self;

    /**
     * Get Seller ID
     *
     * @return int
     */
    public function getSellerId(): int;

    /**
     * @param string $payload
     * @return self
     */
    public function setPayload(string $payload): self;

    /**
     * Get Tracking Payload
     *
     * @return string
     */
    public function getPayload(): string;

    /**
     * @return string
     */
    public function getDate(): string;

    /**
     * @param string $date
     * @return self
     */
    public function setDate(string $date): self;

    /**
     * @return string
     */
    public function getTrackingStatus(): string;

    /**
     * @param string $trackingStatus
     * @return self
     */
    public function setTrackingStatus(string $trackingStatus): self;

    /**
     * @return string
     */
    public function getTrackingNumber(): string;

    /**
     * @param string $trackingNumber
     * @return self
     */
    public function setTrackingNumber(string $trackingNumber): self;
}
