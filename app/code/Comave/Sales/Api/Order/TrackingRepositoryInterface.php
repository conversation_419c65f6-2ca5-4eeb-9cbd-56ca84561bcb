<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Api\Order;

use Comave\Sales\Api\Data\Order\TrackingInterface;

interface TrackingRepositoryInterface
{
    /**
     * @param \Comave\Sales\Api\Data\Order\TrackingInterface $Tracking
     * @return \Comave\Sales\Api\Data\Order\TrackingInterface
     */
    public function save(TrackingInterface $Tracking);

    /**
     * @param int $trackingId
     * @return \Comave\Sales\Api\Data\Order\TrackingInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(int $trackingId);

    /**
     * @param \Comave\Sales\Api\Data\Order\TrackingInterface $Tracking
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(TrackingInterface $Tracking);

    /**
     * @param int $trackingId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById(int $trackingId);

    /**
     * Clear caches instances
     *
     * @return void
     */
    public function clear(): void;
}