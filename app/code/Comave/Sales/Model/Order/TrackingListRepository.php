<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\Order;

use Comave\Sales\Api\Data\Order\TrackingSearchResultInterfaceFactory;
use Comave\Sales\Api\Order\TrackingListRepositoryInterface;
use Comave\Sales\Model\ResourceModel\Order\Tracking\Collection;
use Comave\Sales\Model\ResourceModel\Order\Tracking\CollectionFactory;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;

class TrackingListRepository implements TrackingListRepositoryInterface
{
    /**
     * @param \Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface $collectionProcessor
     * @param \Comave\Sales\Api\Data\Order\TrackingSearchResultInterfaceFactory $searchResultFactory
     * @param \Comave\Sales\Model\ResourceModel\Order\Tracking\CollectionFactory $collectionFactory
     */
    public function __construct(
        private readonly CollectionProcessorInterface $collectionProcessor,
        private readonly TrackingSearchResultInterfaceFactory $searchResultFactory,
        private readonly CollectionFactory $collectionFactory
    ) {
    }

    /**
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return TrackingSearchResultInterfaceFactory
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria)
    {
        /** @var Collection $collection */
        $collection = $this->collectionFactory->create();
        $this->collectionProcessor->process($searchCriteria, $collection);

        /** @var TrackingSearchResultInterfaceFactory $searchResult */
        $searchResult = $this->searchResultFactory->create();
        $searchResult->setSearchCriteria($searchCriteria);
        $collection->setCurPage($searchCriteria->getCurrentPage());
        $collection->setPageSize($searchCriteria->getPageSize());
        $searchResult->setTotalCount($collection->getSize());
        $searchResult->setItems($collection->getItems());

        return $searchResult;
    }
}
