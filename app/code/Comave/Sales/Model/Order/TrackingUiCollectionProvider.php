<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\Order;

use Comave\Sales\Api\Data\Order\TrackingInterface;
use Comave\Sales\Model\ResourceModel\Order\Tracking\CollectionFactory;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Umc\Crud\Ui\CollectionProviderInterface;

class TrackingUiCollectionProvider implements CollectionProviderInterface
{
    /**
     * @param CollectionFactory $factory
     */
    public function __construct(private readonly CollectionFactory $factory)
    {
    }

    /**
     * @return \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
     */
    public function getCollection(): AbstractCollection
    {
        return $this->factory->create();
    }

    /**
     * @param int $orderId
     * @return array
     */
    public function getByOrder(int $orderId): array
    {
        $trackingCollection = $this->getCollection();
        $trackingCollection->addFieldToFilter(TrackingInterface::ORDER_ID, ['eq' => $orderId]);

        return $trackingCollection->getItems();
    }
}
