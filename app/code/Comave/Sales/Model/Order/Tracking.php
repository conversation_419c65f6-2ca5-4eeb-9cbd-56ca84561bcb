<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\Order;

use Comave\Sales\Api\Data\Order\TrackingInterface;
use Comave\Sales\Model\ResourceModel\Order\Tracking as OrderTrackingResourceModel;
use Magento\Framework\Api\ExtensionAttributesInterface;
use Magento\Framework\Model\AbstractExtensibleModel;

class Tracking extends AbstractExtensibleModel implements TrackingInterface
{
    /**
     * Cache tag
     *
     * @var string
     */
    public const string CACHE_TAG = 'comave_seller_order_tracking';
    /**
     * Cache tag
     *
     * @var string
     * phpcs:disable PSR2.Classes.PropertyDeclaration.Underscore,PSR12.Classes.PropertyDeclaration.Underscore
     */
    protected $_cacheTag = self::CACHE_TAG;
    /**
     * Event prefix
     *
     * @var string
     */
    protected $_eventPrefix = 'comave_seller_order_tracking';
    /**
     * Event object
     *
     * @var string
     */
    protected $_eventObject = 'comave_seller_order_tracking';
    //phpcs:enable

    /**
     * Get identities
     *
     * @return array
     */
    public function getIdentities(): array
    {
        return [sprintf("%s_%s", self::CACHE_TAG, $this->getId())];
    }

    /**
     * @return \Magento\Framework\Api\ExtensionAttributesInterface|null
     */
    public function getExtensionAttributes(): ?ExtensionAttributesInterface
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * @param \Magento\Framework\Api\ExtensionAttributesInterface $extensionAttributes
     * @return \Comave\SellerOnboarding\Model\Category\Mapping
     */
    public function setExtensionAttributes(ExtensionAttributesInterface $extensionAttributes): Tracking
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * Initialize resource model
     *
     * @return void
     * phpcs:disable PSR2.Methods.MethodDeclaration.Underscore, PSR12.Methods.MethodDeclaration.Underscore
     */
    protected function _construct()
    {
        $this->_init(OrderTrackingResourceModel::class);
    }

    /**
     * @param int $id
     * @return \Comave\SellerOnboarding\Api\Data\Category\MappingInterface
     */
    public function setTrackingId(int $id): TrackingInterface
    {
        return $this->setData(self::TRACKING_ID, $id);
    }

    /**
     * @return int
     */
    public function getTrackingId(): int
    {
        return (int)$this->getData(self::TRACKING_ID);
    }

    /**
     * @param int $orderId
     * @return self
     */
    public function setOrderId(int $orderId): TrackingInterface
    {
        return $this->setData(self::ORDER_ID, $orderId);
    }

    /**
     * Get Order ID
     *
     * @return int
     */
    public function getOrderId(): int
    {
        return (int)$this->getData(self::ORDER_ID);
    }

    /**
     * @param string $payload
     * @return self
     */
    public function setPayload(string $payload): TrackingInterface
    {
        return $this->setData(self::PAYLOAD, $payload);
    }

    /**
     * Get Tracking Payload
     *
     * @return string
     */
    public function getPayload(): string
    {
        return (string)$this->getData(self::PAYLOAD);
    }


    /**
     * @param int $sellerId
     * @return self
     */
    public function setSellerId(int $sellerId): TrackingInterface
    {
        return $this->setData(self::SELLER_ID, $sellerId);
    }

    /**
     * Get Seller ID
     *
     * @return int
     */
    public function getSellerId(): int
    {
        return (int)$this->getData(self::SELLER_ID);
    }

    /**
     * @return string
     */
    public function getDate(): string
    {
        return (string)$this->getData(self::DATE);
    }

    /**
     * @param string $date
     * @return self
     */
    public function setDate(string $date): TrackingInterface
    {
        return $this->setData(self::DATE, $date);
    }

    /**
     * @return string
     */
    public function getTrackingStatus(): string
    {
        return (string)$this->getData(self::TRACKING_STATUS);
    }

    /**
     * @param string $trackingStatus
     * @return self
     */
    public function setTrackingStatus(string $trackingStatus): TrackingInterface
    {
        return $this->setData(self::TRACKING_STATUS, $trackingStatus);
    }

    /**
     * @return string
     */
    public function getTrackingNumber(): string
    {
        return (string)$this->getData(self::TRACKING_NUMBER);
    }

    /**
     * @param string $trackingNumber
     * @return self
     */
    public function setTrackingNumber(string $trackingNumber): TrackingInterface
    {
        return $this->setData(self::TRACKING_NUMBER, $trackingNumber);
    }
}
