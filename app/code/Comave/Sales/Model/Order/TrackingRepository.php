<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\Order;

use Comave\Sales\Api\Data\Order\TrackingInterface;
use Comave\Sales\Api\Data\Order\TrackingInterfaceFactory;
use Comave\Sales\Api\Order\TrackingRepositoryInterface;
use Comave\Sales\Model\ResourceModel\Order\Tracking as TrackingResourceModel;
use Exception;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class TrackingRepository implements TrackingRepositoryInterface
{
    /**
     * @var TrackingInterface[]
     */
    private array $cache = [];

    /**
     * @param TrackingInterfaceFactory $factory
     * @param TrackingResourceModel $resource
     */
    public function __construct(
        private readonly TrackingInterfaceFactory $factory,
        private readonly TrackingResourceModel $resource
    ) {
    }

    /**
     * @param \Comave\Sales\Api\Data\Order\TrackingInterface $tracking
     * @return \Comave\Sales\Api\Data\Order\TrackingInterface
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     */
    public function save(TrackingInterface $tracking)
    {
        try {
            $this->resource->save($tracking);
        } catch (Exception $exception) {
            throw new CouldNotSaveException(
                __($exception->getMessage())
            );
        }

        return $tracking;
    }

    /**
     * @param int $trackingId
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function deleteById(int $trackingId)
    {
        return $this->delete($this->get($trackingId));
    }

    /**
     * @param \Comave\Sales\Api\Data\Order\TrackingInterface $tracking
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     */
    public function delete(TrackingInterface $tracking)
    {
        try {
            $id = $tracking->getTrackingId();
            $this->resource->delete($tracking);
            unset($this->cache[$id]);
        } catch (Exception $exception) {
            throw new CouldNotDeleteException(
                __($exception->getMessage())
            );
        }

        return true;
    }

    /**
     * @param int $trackingId
     * @return \Comave\Sales\Api\Data\Order\TrackingInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(int $trackingId)
    {
        if (!isset($this->cache[$trackingId])) {
            $orderTracking = $this->factory->create();
            $this->resource->load($orderTracking, $trackingId);
            if (!$orderTracking->getId()) {
                throw new NoSuchEntityException(
                    __('The Order Tracking with the ID "%1" does not exist . ', $trackingId)
                );
            }
            $this->cache[$trackingId] = $orderTracking;
        }

        return $this->cache[$trackingId];
    }

    /**
     * @inheritDoc
     */
    public function clear(): void
    {
        $this->cache = [];
    }
}
