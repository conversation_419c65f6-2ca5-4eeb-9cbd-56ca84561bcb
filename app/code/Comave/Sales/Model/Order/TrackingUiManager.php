<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\Order;

use Comave\Sales\Api\Data\Order\TrackingInterface;
use Comave\Sales\Api\Order\TrackingListRepositoryInterface;
use Comave\Sales\Api\Order\TrackingRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Model\AbstractModel;
use Umc\Crud\Ui\EntityUiManagerInterface;

class TrackingUiManager implements EntityUiManagerInterface
{
    /**
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     * @param \Comave\Sales\Api\Order\TrackingListRepositoryInterface $listRepository
     * @param \Comave\Sales\Api\Order\TrackingRepositoryInterface $repository
     * @param \Comave\Sales\Model\Order\TrackingFactory $factory
     */
    public function __construct(
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly TrackingListRepositoryInterface $listRepository,
        private readonly TrackingRepositoryInterface $repository,
        private readonly TrackingFactory $factory
    ) {
    }

    /**
     * @param \Magento\Framework\Model\AbstractModel $tracking
     * @return void
     */
    public function save(AbstractModel $tracking)
    {
        $this->repository->save($tracking);
    }

    /**
     * @param int $id
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(int $id)
    {
        $this->repository->deleteById($id);
    }

    /**
     * @param int|null $id
     * @return \Magento\Framework\Model\AbstractModel | Tracking | TrackingInterface;
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(?int $id)
    {
        return ($id)
            ? $this->repository->get($id)
            : $this->factory->create();
    }

    /**
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(): array
    {
        return $this->listRepository->getList($this->searchCriteriaBuilder->create())->getItems();
    }

    /**
     * @param int $sellerId
     * @param int $orderId
     * @param string $trackingNumber
     * @return \Comave\Sales\Api\Data\Order\TrackingInterface|null
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function identify(int $sellerId, int $orderId, string $trackingNumber): ?TrackingInterface
    {
        if (!$orderId || !$trackingNumber || !$sellerId) {
            return $this->get(null);
        }

        $trackingId = null;
        $sellerOrderTrackings = $this->listRepository->getList(
            $this->searchCriteriaBuilder
                ->addFilter(TrackingInterface::ORDER_ID, $orderId)
                ->addFilter(TrackingInterface::SELLER_ID, $sellerId)
                ->addFilter(TrackingInterface::TRACKING_NUMBER, $trackingNumber)
                ->create()
        )->getItems();
        foreach ($sellerOrderTrackings as $sellerOrderTracking) {
            $trackingId = (int)$sellerOrderTracking->getTrackingId();
            break;
        }

        return $this->get($trackingId);
    }
}
