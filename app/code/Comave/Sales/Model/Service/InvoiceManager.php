<?php
declare(strict_types=1);

namespace Comave\Sales\Model\Service;

use Magento\Sales\Api\InvoiceRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Sales\Model\Order\Invoice as MagentoInvoice;
use Psr\Log\LoggerInterface;

class InvoiceManager
{
    public function __construct(
        private InvoiceRepositoryInterface $invoiceRepo,
        private SearchCriteriaBuilder $searchBuilder,
        private LoggerInterface $logger
    ) {}

    public function getPaidInvoiceForOrder(int $orderId): ?MagentoInvoice
    {
        $search = $this->searchBuilder
            ->addFilter('order_id', $orderId)
            ->addFilter('state', MagentoInvoice::STATE_PAID)
            ->setPageSize(1)
            ->create();

        $invoices = $this->invoiceRepo->getList($search)->getItems();

        return !empty($invoices) ? reset($invoices) : null;
    }
}
