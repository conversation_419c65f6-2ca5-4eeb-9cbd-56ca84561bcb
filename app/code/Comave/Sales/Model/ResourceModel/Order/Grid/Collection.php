<?php
declare(strict_types=1);

namespace Comave\Sales\Model\ResourceModel\Order\Grid;

use Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult;
use Magento\Sales\Model\ResourceModel\Order\Grid\Collection as OrderGridCollection;

class Collection extends OrderGridCollection
{
    private const SKU_FIELD = 'sku';
    private const EAN_FIELD = 'ean';
    private const SKU_FILTER_FLAG = 'sku_filter_added';
    private const EAN_FILTER_FLAG = 'ean_filter_added';
    private const SALES_ORDER_ITEM_TABLE = 'sales_order_item';

    /**
     * Add field to filter with special handling for SKU and EAN fields.
     *
     * @param string|array $field
     * @param string|int|array|null $condition
     * @return Collection
     */
    public function addFieldToFilter(
        $field,
        $condition = null
    ): Collection {
        if ($field === self::SKU_FIELD && !$this->getFlag(self::SKU_FILTER_FLAG)) {
            return $this->addSkuFilter($condition);
        }

        if ($field === self::EAN_FIELD && !$this->getFlag(self::EAN_FILTER_FLAG)) {
            return $this->addEanFilter($condition);
        }

        return parent::addFieldToFilter($field, $condition);
    }

    /**
     * Add SKU filter to the collection.
     *
     * @param string|int|array|null $condition
     * @return Collection
     */
    private function addSkuFilter($condition): Collection
    {
        $this->setFlag(self::SKU_FILTER_FLAG, true);
        
        $this->getSelect()->joinLeft(
            ['soi_sku' => $this->getTable(self::SALES_ORDER_ITEM_TABLE)],
            'main_table.entity_id = soi_sku.order_id',
            []
        );

        $searchValue = $this->extractSearchValue($condition);
        $this->getSelect()->where('soi_sku.sku = ?', $searchValue);
        $this->getSelect()->group('main_table.entity_id');

        return $this;
    }

    /**
     * Add EAN filter to the collection.
     *
     * @param string|int|array|null $condition
     * @return Collection
     */
    private function addEanFilter($condition): Collection
    {
        $this->setFlag(self::EAN_FILTER_FLAG, true);
        
        $eanAttrId = $this->getEanAttributeId();
        if (!$eanAttrId) {
            $this->getSelect()->where('1=0');
            return $this;
        }

        $this->getSelect()->joinLeft(
            ['soi_ean' => $this->getTable(self::SALES_ORDER_ITEM_TABLE)],
            'main_table.entity_id = soi_ean.order_id',
            []
        );

        $this->getSelect()->joinLeft(
            ['cpe_ean' => $this->getTable('catalog_product_entity')],
            'soi_ean.product_id = cpe_ean.entity_id',
            []
        );

        $joinCondition = $this->hasRowIdColumn()
            ? 'cpe_ean.row_id = ean_attr.row_id'
            : 'cpe_ean.entity_id = ean_attr.entity_id';
        $joinCondition .= ' AND ean_attr.attribute_id = ' . $eanAttrId . ' AND ean_attr.store_id = 0';

        $this->getSelect()->joinLeft(
            ['ean_attr' => $this->getTable('catalog_product_entity_varchar')],
            $joinCondition,
            []
        );

        $searchValue = $this->extractSearchValue($condition);
        $this->getSelect()->where('ean_attr.value = ?', $searchValue);
        $this->getSelect()->group('main_table.entity_id');

        return $this;
    }

    /**
     * Extract search value from condition.
     *
     * @param mixed $condition
     * @return string
     */
    private function extractSearchValue($condition): string
    {
        if (is_array($condition) && isset($condition['like'])) {
            return trim($condition['like'], '%');
        }
        
        return (string)$condition;
    }

    /**
     * Add SKU and EAN data to collection after load.
     *
     * @return SearchResult
     */
    protected function _afterLoad(): SearchResult
    {
        $orderIds = $this->getColumnValues('entity_id');

        if (!empty($orderIds)) {
            $this->addSkuDataToCollection($orderIds);
            $this->addEanDataToCollection($orderIds);
        }

        return parent::_afterLoad();
    }

    /**
     * Add SKU data to each order in the collection.
     *
     * @param array $orderIds
     * @return void
     */
    private function addSkuDataToCollection(array $orderIds): void
    {
        $skuData = $this->getSkuData($orderIds);
        $skusByOrderId = $this->groupDataByOrderId($skuData);

        foreach ($orderIds as $orderId) {
            $order = $this->getItemById($orderId);
            if (!$order) {
                continue;
            }

            $skus = $skusByOrderId[$orderId] ?? [];
            $order->setData(self::SKU_FIELD, $this->formatSkuData($skus));
        }
    }

    /**
     * Add EAN data to each order in the collection.
     *
     * @param array $orderIds
     * @return void
     */
    private function addEanDataToCollection(array $orderIds): void
    {
        $eanData = $this->getEanData($orderIds);
        $eansByOrderId = $this->groupDataByOrderId($eanData);

        foreach ($orderIds as $orderId) {
            $order = $this->getItemById($orderId);
            if (!$order) {
                continue;
            }

            $eans = $eansByOrderId[$orderId] ?? [];
            $order->setData(self::EAN_FIELD, $this->formatEanData($eans));
        }
    }

    /**
     * Get SKU data for the specified order IDs.
     *
     * @param array $orderIds
     * @return array
     */
    private function getSkuData(array $orderIds): array
    {
        $connection = $this->getConnection();

        $select = $connection->select()
            ->from(
                $this->getTable(self::SALES_ORDER_ITEM_TABLE),
                [
                    'order_id',
                    'sku',
                    'qty_ordered',
                ]
            )
            ->where('order_id IN (?)', $orderIds)
            ->where('parent_item_id IS NULL');

        return $connection->fetchAll($select);
    }

    /**
     * Get EAN data for the specified order IDs.
     *
     * @param array $orderIds
     * @return array
     */
    private function getEanData(array $orderIds): array
    {
        $eanAttrId = $this->getEanAttributeId();
        if (!$eanAttrId) {
            return [];
        }

        $connection = $this->getConnection();
        
        $varcharLinkField = $this->hasRowIdColumn() ? 'row_id' : 'entity_id';
        $mainLinkField = $this->hasRowIdColumn() ? 'row_id' : 'entity_id';

        $select = $connection->select()
            ->from(['soi' => $this->getTable(self::SALES_ORDER_ITEM_TABLE)], ['order_id', 'qty_ordered'])
            ->joinLeft(
                ['cpe' => $this->getTable('catalog_product_entity')],
                'soi.product_id = cpe.entity_id',
                [$mainLinkField]
            )
            ->joinLeft(
                ['cpev' => $this->getTable('catalog_product_entity_varchar')],
                'cpe.' . $mainLinkField . ' = cpev.' . $varcharLinkField . ' AND cpev.attribute_id = ' . $eanAttrId . ' AND cpev.store_id = 0',
                ['value as ean']
            )
            ->where('soi.order_id IN (?)', $orderIds)
            ->where('soi.parent_item_id IS NULL')
            ->where('cpev.value IS NOT NULL');

        return $connection->fetchAll($select);
    }

    /**
     * Group data by order ID.
     *
     * @param array $data
     * @return array
     */
    private function groupDataByOrderId(array $data): array
    {
        $grouped = [];
        foreach ($data as $item) {
            $orderId = $item['order_id'];
            if (!isset($grouped[$orderId])) {
                $grouped[$orderId] = [];
            }
            $grouped[$orderId][] = $item;
        }
        return $grouped;
    }

    /**
     * Format SKU data for display.
     *
     * @param array $skus
     * @return string
     */
    private function formatSkuData(array $skus): string
    {
        if (empty($skus)) {
            return '';
        }

        $formatted = [];
        foreach ($skus as $item) {
            $qty = (int)$item['qty_ordered'];
            $formatted[] = '<div>' . $item['sku'] . ($qty > 1 ? ' (x' . $qty . ')' : '') . '</div>';
        }

        return implode('', $formatted);
    }

    /**
     * Format EAN data for display.
     *
     * @param array $eans
     * @return string
     */
    private function formatEanData(array $eans): string
    {
        if (empty($eans)) {
            return '';
        }

        $formatted = [];
        foreach ($eans as $item) {
            if (!empty($item['ean'])) {
                $qty = (int)$item['qty_ordered'];
                $formatted[] = '<div>' . $item['ean'] . ($qty > 1 ? ' (x' . $qty . ')' : '') . '</div>';
            }
        }

        return implode('', $formatted);
    }

    /**
     * Get EAN attribute ID.
     *
     * @return int|null
     */
    private function getEanAttributeId(): ?int
    {
        $connection = $this->getConnection();

        try {
            $select = $connection->select()
                ->from(
                    ['ea' => $connection->getTableName('eav_attribute')],
                    ['attribute_id']
                )
                ->where('ea.entity_type_id = ?', 4)
                ->where('ea.attribute_code = ?', 'ean');

            $attributeId = $connection->fetchOne($select);
            return $attributeId ? (int)$attributeId : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Check if catalog_product_entity has row_id column.
     *
     * @return bool
     */
    private function hasRowIdColumn(): bool
    {
        $connection = $this->getConnection();

        try {
            $columns = $connection->describeTable(
                $connection->getTableName('catalog_product_entity')
            );
            return isset($columns['row_id']);
        } catch (\Exception $e) {
            return false;
        }
    }
}
