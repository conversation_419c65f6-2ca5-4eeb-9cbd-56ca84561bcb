<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\ResourceModel\Order\Tracking;

use Umc\Crud\Model\ResourceModel\Collection\AbstractCollection;

/**
 * @api
 */
class Collection extends AbstractCollection
{
    /**
     * @var string
     * phpcs:disable PSR2.Classes.PropertyDeclaration.Underscore,PSR12.Classes.PropertyDeclaration.Underscore
     */
    protected $_idFieldName = 'tracking_id';
    //phpcs: enable

    /**
     * Define resource model
     *
     * @return void
     * @codeCoverageIgnore
     * //phpcs:disable PSR2.Methods.MethodDeclaration.Underscore, PSR12.Methods.MethodDeclaration.Underscore
     */
    protected function _construct()
    {
        $this->_init(
            \Comave\Sales\Model\Order\Tracking::class,
            \Comave\Sales\Model\ResourceModel\Order\Tracking::class
        );
        //phpcs: enable
    }
}
