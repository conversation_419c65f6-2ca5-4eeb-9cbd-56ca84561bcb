<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Service\Order;

use Comave\BigBuy\Model\ConfigProvider;
use Comave\Sales\Model\Order\TrackingUiCollectionProvider;
use Comave\SellerApi\Api\ConfigurableApiInterfaceFactory;
use Magento\Sales\Api\Data\OrderInterface;
use Psr\Log\LoggerInterface;

class TrackingService
{
    /**
     * @param \Magento\Framework\Serialize\SerializerInterface $serializer
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Comave\SellerApi\Service\RequestHandler $requestHandler
     * @param \Comave\BigBuy\Model\ConfigProvider $configProvider
     * @param \Comave\SellerApi\Api\ConfigurableApiInterfaceFactory $configurableApiFactory
     * @param \Comave\BigBuy\Model\OrderLinkUiManager $orderLinkUiManager
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly ConfigProvider $configProvider,
        private readonly TrackingUiCollectionProvider $trackingUiCollectionProvider,
    ) {
    }

    /**
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @return array
     */
    public function fetch(OrderInterface $order): array
    {
        if (!$this->configProvider->isOrderTrackingSyncEnable()) {
            $this->logger->warning('BigBuy Order Tracking Sync process is disabled');

            return [];
        }

        return $this->trackingUiCollectionProvider->getByOrder((int)$order->getId());
    }
}
