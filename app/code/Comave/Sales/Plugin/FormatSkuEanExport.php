<?php
declare(strict_types=1);

namespace Comave\Sales\Plugin;

use Magento\Framework\Api\Search\DocumentInterface;
use Magento\Ui\Model\Export\MetadataProvider;

class FormatSkuEanExport
{
    /**
     * Format SKU and EAN data for export by removing HTML and creating semicolon-delimited lists.
     *
     * @param MetadataProvider $subject
     * @param DocumentInterface $document
     * @param array $fields
     * @param array $options
     * @return array
     */
    public function beforeGetRowData(
        MetadataProvider $subject,
        DocumentInterface $document,
        $fields,
        $options
    ): array {
        if ($skuData = $document->getData('sku')) {
            $document->setData('sku', $this->formatDataForExport($skuData));
        }

        if ($eanData = $document->getData('ean')) {
            $document->setData('ean', $this->formatDataForExport($eanData));
        }

        return [
            $document,
            $fields,
            $options,
        ];
    }

    /**
     * Format HTML data for CSV export.
     *
     * @param string $htmlData
     * @return string
     */
    private function formatDataForExport(string $htmlData): string
    {
        if (empty($htmlData)) {
            return '';
        }

        $decodedData = html_entity_decode($htmlData);
        $explodedItems = explode('</div><div>', $decodedData);

        foreach ($explodedItems as $itemId => $explodedItem) {
            $explodedItems[$itemId] = trim(strip_tags($explodedItem));
        }

        return implode(';', array_filter($explodedItems));
    }
}
