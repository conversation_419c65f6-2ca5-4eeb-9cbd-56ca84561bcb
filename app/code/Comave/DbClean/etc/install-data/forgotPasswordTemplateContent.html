{{template config_path="design/email/header_template"}}

<table align="center" style="background-color:#000; text-align:center; width: 660px; display:none !important;">
    <tbody>
    <tr>
        <td class="dark"  style="padding-bottom:8px; padding-top:5px; background-color:#000">
            <h3 style="text-align: center; text-transform: uppercase;">

            </h3>
        </td>
    </tr>
    <tr>
        <td class="dark" align="center" style="padding-bottom:0px; background-color:#000">
            <h1 style="text-align: center; margin: 0 !important">

            </h1>
        </td>
    </tr>
    <tr>
        <td class="dark" align="center" style="padding-bottom:8px; background-color:#000">
            <h3 style="text-align: center;">

            </h3>
        </td>
    </tr>
    </tbody>
</table>
<div style="padding: 0 10px 0 10px;">
    <p class="greeting" style="margin-top: 15px; margin-left:20px;">{{trans "Hello %name," name=$customer.name}}</p>

    <p style="padding-left:20px;"> {{trans " There was recently a request to change the password on your account."}}</p>
    <p style="padding-left:20px;"> {{trans "If you requested this change, then please select the 'Reset Password' box below:"}}</p>

    <table class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
        <tr>
            <td>
                <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center" width="50%">
                    <tr>
                        <td align="center" style="padding: 8px 0 !important; ">
                            {{if is_seller}}
                            <a href="{{config path='web/secure/base_url'}}customer/account/createPassword?id={{var customer.id}}&token={{var customer.rp_token}}" target="_blank" style="font-weight: bold">{{trans "Reset Password"}}</a>
                            {{else}}
                            <a href="{{config path='web/secure/base_url'}}customer/account/createPassword?id={{var customer.id}}&token={{var customer.rp_token}}" target="_blank" style="font-weight: bold">{{trans "Reset Password"}}</a>
                            <!-- frontend uses unsecure url to be updated when the FE functionality is done -->
                            <!--a href="{{config path='web/unsecure/base_url'}}customer/account/createPassword?id={{var customer.id}}&token={{var customer.rp_token}}" target="_blank" style="font-weight: bold">{{trans "Reset Password"}}</a-->
                            {{/if}}
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

    <p  style="padding-left:20px;">{{trans "If you didn't initiate this request, please contact us <NAME_EMAIL>"}}</p>

    <p  style="padding-left:20px;">{{trans "Regards,"}}</p>
    <p  style="padding-left:20px;color:#d91f26 !important;font-weight: bold !important;">{{trans "ComAve"}}</p>
</div>

{{template config_path="design/email/footer_template"}}
