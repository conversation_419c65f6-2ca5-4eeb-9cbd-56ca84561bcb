<?php
declare(strict_types=1);

namespace Comave\DbClean\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Store\Api\StoreRepositoryInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Psr\Log\LoggerInterface;

class UpdateEnglishStoreFooterEmailTemplate implements DataPatchInterface
{
    private const STORE_CODE_EN = 'en_store';
    private const XML_PATH_EMAIL_FOOTER_TEMPLATE = 'design/email/footer_template';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly StoreRepositoryInterface $storeRepository,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $storeId = $this->getStoreIdByCode(self::STORE_CODE_EN);
        if ($storeId === null) {
            $this->logger->warning("Store with code 'en_store' not found.");
            return;
        }

        $emailFooterTemplateConfigId = $this->getEmailFooterTemplateConfigIdForStore($storeId);
        if ($emailFooterTemplateConfigId === null) {
            $this->logger->info("No footer template config found for store ID: $storeId.");
            return;
        }

        $this->deleteConfigById($emailFooterTemplateConfigId);

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * @param string $storeCode
     * @return int|null
     */
    private function getStoreIdByCode(string $storeCode): ?int
    {
        try {
            $store = $this->storeRepository->get($storeCode);
            return (int) $store->getId();
        } catch (\Magento\Framework\Exception\NoSuchEntityException $e) {
            return null;
        }
    }

    /**
     * @param int $storeId
     * @return int|null
     */
    private function getEmailFooterTemplateConfigIdForStore(int $storeId): ?int
    {
        try {
            $configId = $this->moduleDataSetup->getConnection()->fetchOne(
                $this->moduleDataSetup->getConnection()
                    ->select()
                    ->from('core_config_data', 'config_id')
                    ->where('scope = ?', 'stores')
                    ->where('scope_id = ?', $storeId)
                    ->where('path = ?', self::XML_PATH_EMAIL_FOOTER_TEMPLATE)
            );
            return $configId !== false ? (int) $configId : null;
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            return null;
        }
    }

    /**
     * @param int $configId
     * @return void
     */
    public function deleteConfigById(int $configId): void
    {
        try {
            $deletedConfig = $this->moduleDataSetup->getConnection()->delete(
                'core_config_data',
                [
                    'config_id = ?' => $configId
                ]
            );

            if ($deletedConfig) {
                $this->logger->info("Deleted config entry with config_id = $configId.");
            } else {
                $this->logger->warning("No config entry found to delete for config_id = $configId.");
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [
            \Comave\DbClean\Setup\Patch\Data\UpdateEnglishStoreTheme::class,
        ];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}
