<?php
declare(strict_types=1);

namespace Comave\DbClean\Setup\Patch\Data;

use Magento\Framework\App\Config\Storage\WriterInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\View\DesignInterface;
use Magento\Store\Api\StoreRepositoryInterface;
use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;

class UpdateEnglishStoreHeaderEmailTemplate implements DataPatchInterface
{
    private const STORE_CODE_EN = 'en_store';
    private const XML_PATH_EMAIL_HEADER_TEMPLATE = 'design/email/header_template';
    private const PATH_ORID_HEADER_TEMPLATE = 'design_email_header_template';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly StoreRepositoryInterface $storeRepository,
        private readonly WriterInterface $configWriter,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $storeId = $this->getStoreIdByCode(self::STORE_CODE_EN);
        if ($storeId === null) {
            $this->logger->warning("Store with code 'en_store' not found.");
            return;
        }

        $emailHeaderTemplateConfigId = $this->getEmailHeaderTemplateConfigByStore($storeId);
        if ($emailHeaderTemplateConfigId !== null) {
            $this->deleteConfigById($emailHeaderTemplateConfigId);
        }

        $headerTemplateId = $this->getHeaderEmailTemplateId('Email Header - WeltPixel', self::PATH_ORID_HEADER_TEMPLATE);
        if ($headerTemplateId === null) {
            return;
        }
        $this->setThemeHeaderEmailTemplate($storeId, self::XML_PATH_EMAIL_HEADER_TEMPLATE, (string)$headerTemplateId);

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * @param string $storeCode
     * @return int|null
     */
    private function getStoreIdByCode(string $storeCode): ?int
    {
        try {
            $store = $this->storeRepository->get($storeCode);
            return (int) $store->getId();
        } catch (\Magento\Framework\Exception\NoSuchEntityException $e) {
            return null;
        }
    }

    /**
     * @param int $storeId
     * @return int|null
     */
    private function getEmailHeaderTemplateConfigByStore(int $storeId): ?int
    {
        try {
            $configId = $this->moduleDataSetup->getConnection()->fetchOne(
                $this->moduleDataSetup->getConnection()
                    ->select()
                    ->from('core_config_data', 'config_id')
                    ->where('scope = ?', 'stores')
                    ->where('scope_id = ?', $storeId)
                    ->where('path = ?', self::XML_PATH_EMAIL_HEADER_TEMPLATE)
            );
            return $configId != false ? (int) $configId : null;
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            return null;
        }
    }

    /**
     * @param int $configId
     * @return void
     */
    public function deleteConfigById(int $configId): void
    {
        try {
            $deletedConfig = $this->moduleDataSetup->getConnection()->delete(
                'core_config_data',
                [
                    'config_id = ?' => $configId
                ]
            );

            if ($deletedConfig) {
                $this->logger->info("Deleted config_id = $configId.");
            } else {
                $this->logger->warning("No config entry found to delete for config_id = $configId.");
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }

    /**
     * @param string $templateCode
     * @param string $origTemplateCode
     * @return int|null
     */
    private function getHeaderEmailTemplateId(string $templateCode, string $origTemplateCode): ?int
    {
        try {
            $connection = $this->moduleDataSetup->getConnection();
            $templateId = $connection->fetchOne(
                $connection->select()
                    ->from('email_template', 'template_id')
                    ->where('template_code = ?', $templateCode)
                    ->where('orig_template_code = ?', $origTemplateCode)
                    ->limit(1)
            );

            return $templateId != false ? (int) $templateId : null;
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf(
                    'Error fetching email template ID for template_code="%s", orig_template_code="%s": %s',
                    $templateCode,
                    $origTemplateCode,
                    $e->getMessage()
                )
            );
            return null;
        }
    }

    /**
     * @param int $storeId
     * @param string $path
     * @param string $value
     * @return void
     */
    private function setThemeHeaderEmailTemplate(int $storeId, string $path, string $value): void
    {
        try {
            $this->configWriter->save(
                $path,
                $value,
                ScopeInterface::SCOPE_STORES,
                $storeId
            );

            $this->logger->info(
                sprintf(
                    'HeaderEmailTemplate config has been set: %s, %d',
                    $path,
                    $value
                )
            );
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf(
                    'HeaderEmailTemplate config has not been set %s, %d, %s',
                    $path,
                    $value,
                    $e->getMessage()
                )
            );
        }
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [
            \Comave\DbClean\Setup\Patch\Data\UpdateEnglishStoreTheme::class,
            \Comave\DbClean\Setup\Patch\Data\UpdateEnglishStoreFooterEmailTemplate::class
        ];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}
