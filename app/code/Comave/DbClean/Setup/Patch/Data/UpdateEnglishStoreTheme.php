<?php
declare(strict_types=1);

namespace Comave\DbClean\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Store\Api\StoreRepositoryInterface;
use Magento\Theme\Model\ResourceModel\Theme\CollectionFactory as ThemeCollectionFactory;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Config\Storage\WriterInterface;
use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;
use Magento\Framework\View\DesignInterface;

class UpdateEnglishStoreTheme implements DataPatchInterface
{
    private const THEME_PEARL_WELTPIXEL_CUSTOM = 'Pearl/weltpixel_custom';
    private const STORE_CODE_EN = 'en_store';

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param StoreManagerInterface $storeManager
     * @param StoreRepositoryInterface $storeRepository
     * @param ThemeCollectionFactory $themeCollectionFactory
     * @param ScopeConfigInterface $scopeConfig
     * @param WriterInterface $configWriter
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly StoreManagerInterface $storeManager,
        private readonly StoreRepositoryInterface $storeRepository,
        private readonly ThemeCollectionFactory $themeCollectionFactory,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly WriterInterface $configWriter,
        private readonly LoggerInterface $logger,
    ) {}

    /**
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $storeId = $this->getStoreIdByCode(self::STORE_CODE_EN);
        if ($storeId === null) {
            $this->logger->warning("Store with code 'en_store' not found.");
            return;
        }

        $themeId = $this->getThemeIdByPath(self::THEME_PEARL_WELTPIXEL_CUSTOM);
        if ($themeId === null) {
            $this->logger->warning(sprintf("Theme '%s' not found.", self::THEME_PEARL_WELTPIXEL_CUSTOM));
            return;
        }

        $isThemeSet = $this->isThemeSetForStore($storeId, $themeId);
        if ($isThemeSet) {
            return;
        }

        $this->setThemeForStore($storeId, $themeId);

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * @param string $storeCode
     * @return int|null
     */
    private function getStoreIdByCode(string $storeCode): ?int
    {
        try {
            $store = $this->storeRepository->get($storeCode);
            return (int) $store->getId();
        } catch (\Magento\Framework\Exception\NoSuchEntityException $e) {
            return null;
        }
    }

    /**
     * @param string $themePath
     * @return int|null
     */
    private function getThemeIdByPath(string $themePath): ?int
    {
        $collection = $this->themeCollectionFactory->create()
            ->addFieldToFilter('theme_path', $themePath)
            ->setPageSize(1);

        return (int)$collection->getFirstItem()?->getId() ?? null;
    }

    /**
     * @param int $storeId
     * @param int $themeId
     * @return bool
     */
    private function isThemeSetForStore(int $storeId, int $themeId): bool
    {
        $configuredThemeId = $this->scopeConfig->getValue(
            DesignInterface::XML_PATH_THEME_ID,
            ScopeInterface::SCOPE_STORES,
            $storeId
        );

        return (int) $configuredThemeId === $themeId;
    }

    /**
     * @param int $storeId
     * @param int $themeId
     * @return void
     */
    private function setThemeForStore(int $storeId, int $themeId): void
    {
        try {
            $this->configWriter->save(
                DesignInterface::XML_PATH_THEME_ID,
                $themeId,
                ScopeInterface::SCOPE_STORES,
                $storeId
            );
            $this->logger->info(sprintf(
                'Successfully set theme_id = %d for store_id = %d.',
                $themeId,
                $storeId
            ));
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage());
        }
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}
