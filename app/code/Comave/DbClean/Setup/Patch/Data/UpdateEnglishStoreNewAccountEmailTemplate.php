<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\DbClean\Setup\Patch\Data;

use Comave\DbClean\Service\EmailTemplateConfigProvider;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Filesystem\Io\File as IoFile;
use Magento\Framework\Module\Dir;
use Magento\Framework\Module\Dir\Reader as DirReader;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Psr\Log\LoggerInterface;

class UpdateEnglishStoreNewAccountEmailTemplate implements DataPatchInterface
{
    private const string CONFIG_PATH_NEW_ACCOUNT_EMAIL_TEMPLATE = 'customer/create_account/email_template';
    private const string CONFIG_PATH_ORIG_NEW_ACCOUNT_TEMPLATE_CODE = 'New Account - comave';
    private const string CONFIG_PATH_ORIG_NEW_ACCOUNT_EMAIL_TEMPLATE = 'customer_new_account_weltpixel';
    private const string DEFAULT_SOURCE_DIR = 'install-data';
    private const string NEW_ACCOUNT_TEMPLATE_CONTENT = 'newAccountTemplateContent.html';
    private const string NEW_ACCOUNT_TEMPLATE_SUBJECT = 'newAccountTemplateSubject.html';
    private const string NEW_ACCOUNT_TEMPLATE_VARIABLES = 'newAccountTemplateVariables.html';
    private const string NEW_ACCOUNT_TEMPLATE_NAME = 'newAccountTemplateName.html';

    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EmailTemplateConfigProvider $emailTemplateConfigProvider,
        private readonly DirReader $dirReader,
        private readonly IoFile $ioFile,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $storeId = $this->emailTemplateConfigProvider->getStoreIdByCode(EmailTemplateConfigProvider::STORE_CODE_EN);
        if ($storeId === null) {
            $this->logger->warning("Store with code 'en_store' not found.");

            return;
        }

        $emailNewAccountTemplateConfigId = $this->emailTemplateConfigProvider->getEmailTemplateConfigByStore(
            (int)$storeId,
            'websites',
            self::CONFIG_PATH_NEW_ACCOUNT_EMAIL_TEMPLATE
        );

        $newAccountTemplateId = $this->emailTemplateConfigProvider->getEmailTemplateId(
            self::CONFIG_PATH_ORIG_NEW_ACCOUNT_TEMPLATE_CODE,
            self::CONFIG_PATH_ORIG_NEW_ACCOUNT_EMAIL_TEMPLATE
        );

        if ($newAccountTemplateId !== null && $newAccountTemplateId === $emailNewAccountTemplateConfigId) {
            return;
        }

        $this->emailTemplateConfigProvider->setEmailTemplate(
            (int)$storeId,
            self::CONFIG_PATH_ORIG_NEW_ACCOUNT_EMAIL_TEMPLATE,
            (string)$newAccountTemplateId
        );

        $templateName = $this->getFileContent(self::NEW_ACCOUNT_TEMPLATE_NAME);
        $templateContent = $this->getFileContent(self::NEW_ACCOUNT_TEMPLATE_CONTENT);
        $templateSubject = $this->getFileContent(self::NEW_ACCOUNT_TEMPLATE_SUBJECT);
        $templateVariables = $this->getFileContent(self::NEW_ACCOUNT_TEMPLATE_VARIABLES);
        if (empty($templateContent) || empty($templateSubject) || empty($templateVariables) || empty($templateName)) {
            $this->logger->error('Failed to load template files');

            return;
        }

        try {
            $this->moduleDataSetup->getConnection()->insert(
                $this->moduleDataSetup->getTable('email_template'),
                [
                    'template_code' => $templateName,
                    'template_text' => $templateContent,
                    'template_subject' => $templateSubject,
                    'orig_template_variables' => $templateVariables,
                    'orig_template_code' => self::CONFIG_PATH_ORIG_NEW_ACCOUNT_EMAIL_TEMPLATE,
                    'template_type' => 2,
                    'is_legacy' => 1,
                ]
            );
            $lastInsertId = $this->moduleDataSetup->getConnection()->lastInsertId(
                $this->moduleDataSetup->getTable('email_template')
            );
            $this->emailTemplateConfigProvider->updateTemplateConfig(
                (int)$storeId,
                'websites',
                self::CONFIG_PATH_NEW_ACCOUNT_EMAIL_TEMPLATE,
                (string)$lastInsertId
            );
        } catch (CouldNotSaveException $e) {
            $this->logger->error('Failed to update the email template '.$e->getMessage());
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * @return string
     */
    private function getSourceDir(): string
    {
        return $this->dirReader->getModuleDir(Dir::MODULE_ETC_DIR, 'Comave_DbClean')
            .'/'.self::DEFAULT_SOURCE_DIR.'/';
    }

    /**
     * @param string $file
     * @return string
     */
    private function getFileContent(string $file): string
    {
        try {
            return $this->ioFile->read($this->getSourceDir().$file);
        } catch (NoSuchEntityException $e) {
            $this->logger->error(
                sprintf(
                    'Template file "%s" does not exist. Error: %s',
                    $file,
                    $e->getMessage()
                )
            );

            return '';
        }
    }

    /**
     * {@inheritdoc}
     */
    public static function getDependencies(): array
    {
        return [
            UpdateEnglishStoreTheme::class,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getAliases(): array
    {
        return [];
    }
}
