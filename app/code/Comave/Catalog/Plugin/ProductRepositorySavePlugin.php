<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Plugin;

use Closure;
use Comave\Catalog\Model\Product\BlacklistUiManager;
use Magento\Catalog\Model\ProductRepository;

class ProductRepositorySavePlugin
{
    /**
     * @param \Comave\Catalog\Model\Product\BlacklistUiManager $blacklistUiManager
     */
    public function __construct(
        private readonly BlacklistUiManager $blacklistUiManager,
    ) {
    }

    /**
     * @param \Magento\Catalog\Model\ProductRepository $subject
     * @param \Closure $proceed
     * @param mixed ...$args
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function aroundSave(
        ProductRepository $subject,
        Closure $proceed,
        ...$args
    ): void {
        if (!$this->blacklistUiManager->isBlacklisted($args[0]->getSku())) {
            $proceed(...$args);
        }
    }
}