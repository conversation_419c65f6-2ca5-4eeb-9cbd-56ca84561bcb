{"name": "comave/module-catalog-blacklist", "version": "1.0.0", "description": "The Catalog Blacklist feature allows you to block selected products from triggering automated updates requests or appearing in any public-facing widgets, feeds, or displays. This is especially useful for excluding items such as free samples, pre-orders, out-of-stock items, or custom SKUs from your catalog flow.", "type": "magento2-module", "require": {"magento/framework": "*"}, "autoload": {"files": ["registration.php"], "psr-4": {"Comave\\Catalog\\": ""}}}