<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Observer;

use Comave\Catalog\Model\Product\BlacklistUiManager;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Psr\Log\LoggerInterface;
use ReflectionClassFactory;

class SetSaveAllowed implements ObserverInterface
{
    /**
     * @param \Comave\Catalog\Model\Product\BlacklistUiManager $blacklistUiManager
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        private readonly ReflectionClassFactory $reflectionClassFactory,
        private readonly BlacklistUiManager $blacklistUiManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        try {
            $product = $observer->getProduct();
            $reflectionClass = $this->reflectionClassFactory->create(['objectOrClass' => $product]);
            $reflectionProperty = $reflectionClass->getProperty('_dataSaveAllowed');
            $reflectionProperty->setAccessible(true);
            $reflectionProperty->setValue($product, !$this->blacklistUiManager->isBlacklisted($product->getSku()));
        } catch (\Exception $exception) {
            $this->logger->warning('Could not prevent product for being saved.', [
                'reason' => $exception->getMessage(),
            ]);
        }
    }
}
