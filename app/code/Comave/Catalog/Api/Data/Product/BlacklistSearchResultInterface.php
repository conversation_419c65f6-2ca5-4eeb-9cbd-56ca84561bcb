<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Api\Data\Product;

use Magento\Framework\Api\SearchCriteriaInterface;

interface BlacklistSearchResultInterface
{
    /**
     * get items
     *
     * @return \Comave\Catalog\Api\Data\Product\BlacklistInterface[]
     */
    public function getItems();

    /**
     * Set items
     *
     * @param \Comave\Catalog\Api\Data\Product\BlacklistInterface[] $items
     * @return $this
     */
    public function setItems(array $items);

    /**
     * @param SearchCriteriaInterface $searchCriteria
     * @return $this
     */
    public function setSearchCriteria(SearchCriteriaInterface $searchCriteria);

    /**
     * @param int $count
     * @return $this
     */
    public function setTotalCount($count);
}
