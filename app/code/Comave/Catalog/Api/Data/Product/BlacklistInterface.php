<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Api\Data\Product;

use Magento\Framework\Api\ExtensibleDataInterface;

interface BlacklistInterface extends ExtensibleDataInterface
{
    public const int PRODUCT_STATUS_UNTRACKED = 0;
    public const int PRODUCT_STATUS_DISCONTINUED = 1;
    public const string BLACKLIST_ID = 'blacklist_id';
    public const string PRODUCT_SKU = 'product_sku';
    public const string PRODUCT_NAME = 'product_name';
    public const string PRODUCT_STATUS = 'product_status';

    /**
     * @param $id
     * @return self
     */
    public function setId($id);

    /**
     * @return int
     */
    public function getId();

    /**
     * @param int $id
     * @return self
     */
    public function setBlacklistId(int $id): self;

    /**
     * @return int|null
     */
    public function getBlacklistId(): ?int;


    /**
     * @return string
     */
    public function getProductSku(): string;

    /**
     * @param string $productSku
     * @return self
     */
    public function setProductSku(string $productSku): self;

    /**
     * @return string
     */
    public function getProductName(): ?string;

    /**
     * @param string $productName
     * @return self
     */
    public function setProductName(?string $productName): self;

    /**
     * @return int
     */
    public function getProductStatus(): int;

    /**
     * @param int $productStatus
     * @return self
     */
    public function setProductStatus(int $productStatus): self;
}
