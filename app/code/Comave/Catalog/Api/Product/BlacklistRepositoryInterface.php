<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Api\Product;

use Comave\Catalog\Api\Data\Product\BlacklistInterface;

interface BlacklistRepositoryInterface
{
    /**
     * @param \Comave\Catalog\Api\Data\Product\BlacklistInterface $Blacklist
     * @return \Comave\Catalog\Api\Data\Product\BlacklistInterface
     */
    public function save(BlacklistInterface $Blacklist);

    /**
     * @param int $blacklistId
     * @return \Comave\Catalog\Api\Data\Product\BlacklistInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(int $blacklistId);

    /**
     * @param \Comave\Catalog\Api\Data\Product\BlacklistInterface $Blacklist
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(BlacklistInterface $Blacklist);

    /**
     * @param int $blacklistId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById(int $blacklistId);

    /**
     * Clear caches instances
     *
     * @return void
     */
    public function clear(): void;
}