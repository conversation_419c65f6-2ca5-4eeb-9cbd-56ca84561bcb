<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Ui\Form\DataModifier;

use Magento\Catalog\Model\ProductRepository;
use Magento\Framework\Model\AbstractModel;
use Umc\Crud\Ui\Form\DataModifierInterface;

class Product implements DataModifierInterface
{
    /**
     * @param \Magento\Catalog\Model\ProductRepository $productRepository
     * @param array $fields
     */
    public function __construct(
        private readonly ProductRepository $productRepository,
        private readonly array $fields
    ) {
    }

    /**
     * @param \Magento\Framework\Model\AbstractModel $model
     * @param array $data
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function modifyData(AbstractModel $model, array $data): array
    {
        foreach ($this->fields as $field) {
            if (!array_key_exists($field, $data)) {
                continue;
            }
            $data[$field] = $this->productRepository->get($data[$field])->getName();
        }

        return $data;
    }
}
