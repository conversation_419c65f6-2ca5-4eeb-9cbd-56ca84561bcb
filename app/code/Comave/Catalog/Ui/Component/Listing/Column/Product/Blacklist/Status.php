<?php
declare(strict_types=1);

namespace Comave\Catalog\Ui\Component\Listing\Column\Product\Blacklist;

use Comave\Catalog\Api\Data\Product\BlacklistInterface;
use Magento\Framework\Data\OptionSourceInterface;

class Status implements OptionSourceInterface
{
    /**
     * Options getter
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        return [
            ['value' => BlacklistInterface::PRODUCT_STATUS_UNTRACKED, 'label' => __('Untracked')],
            ['value' => BlacklistInterface::PRODUCT_STATUS_DISCONTINUED, 'label' => __('Discontinued')],
        ];
    }

    /**
     * Get options in "key-value" format
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            BlacklistInterface::PRODUCT_STATUS_UNTRACKED => __('Untracked'),
            BlacklistInterface::PRODUCT_STATUS_DISCONTINUED => __('Discontinued'),
        ];
    }
}
