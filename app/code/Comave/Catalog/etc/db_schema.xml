<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="comave_catalog_product_blacklist" resource="default" engine="innodb"
           comment="Comave Catalog Product Blacklist">
        <column xsi:type="int" name="blacklist_id" padding="10" unsigned="true" nullable="false" identity="true"
                comment="Blacklist ID"/>
        <column xsi:type="varchar" name="product_sku" nullable="false" length="255" comment="Product Sku"/>
        <column xsi:type="varchar" name="product_name" nullable="true" length="255" comment="Product Name"/>
        <column xsi:type="smallint" name="product_status" unsigned="true" nullable="false" identity="false" default="1"
                comment="Product Status"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created at"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Updated At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="blacklist_id"/>
        </constraint>
        <index referenceId="COMAVE_CATALOG_PRODUCT_BLACKLIST_PRODUCT_STATUS" indexType="btree">
            <column name="product_status"/>
        </index>
        <constraint xsi:type="unique" referenceId="COMAVE_CATALOG_PRODUCT_BLACKLIST_PRODUCT_SKU_CONSTRAINT">
            <column name="product_sku"/>
        </constraint>
    </table>
</schema>
