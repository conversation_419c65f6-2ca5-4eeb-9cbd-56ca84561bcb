<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="catalog_product_delete_after_done">
        <observer name="comave_catalog_blacklist_product" instance="Comave\Catalog\Observer\BlacklistProduct"/>
    </event>
    <event name="catalog_product_save_before">
        <observer name="comave_catalog_product_save_before" instance="Comave\Catalog\Observer\SetSaveAllowed"/>
    </event>
</config>