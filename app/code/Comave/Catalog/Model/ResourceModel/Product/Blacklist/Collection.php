<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Model\ResourceModel\Product\Blacklist;

use Umc\Crud\Model\ResourceModel\Collection\AbstractCollection;

/**
 * @api
 */
class Collection extends AbstractCollection
{
    /**
     * @var string
     * phpcs:disable PSR2.Classes.PropertyDeclaration.Underscore,PSR12.Classes.PropertyDeclaration.Underscore
     */
    protected $_idFieldName = 'blacklist_id';
    //phpcs: enable

    /**
     * Define resource model
     *
     * @return void
     * @codeCoverageIgnore
     * //phpcs:disable PSR2.Methods.MethodDeclaration.Underscore, PSR12.Methods.MethodDeclaration.Underscore
     */
    protected function _construct()
    {
        $this->_init(
            \Comave\Catalog\Model\Product\Blacklist::class,
            \Comave\Catalog\Model\ResourceModel\Product\Blacklist::class
        );
        //phpcs: enable
    }
}
