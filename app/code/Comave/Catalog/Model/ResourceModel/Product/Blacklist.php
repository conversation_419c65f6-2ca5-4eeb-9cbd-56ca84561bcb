<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Model\ResourceModel\Product;

use Umc\Crud\Model\ResourceModel\AbstractModel;

class Blacklist extends AbstractModel
{
    /**
     * Initialize resource model
     *
     * @return void
     * @codeCoverageIgnore
     * //phpcs:disable PSR2.Methods.MethodDeclaration.Underscore, PSR12.Methods.MethodDeclaration.Underscore
     */
    protected function _construct()
    {
        $this->_init('comave_catalog_product_blacklist', 'blacklist_id');
    }
    //phpcs: enable
}