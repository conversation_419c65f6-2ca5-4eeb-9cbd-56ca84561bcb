<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Model\Product;

use Comave\Catalog\Api\Data\Product\BlacklistInterface;
use Comave\Catalog\Api\Data\Product\BlacklistInterfaceFactory;
use Comave\Catalog\Api\Product\BlacklistRepositoryInterface;
use Comave\Catalog\Model\ResourceModel\Product\Blacklist as BlacklistResourceModel;
use Exception;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

class BlacklistRepository implements BlacklistRepositoryInterface
{
    /**
     * @var BlacklistInterface[]
     */
    private array $cache = [];

    /**
     * @param BlacklistInterfaceFactory $factory
     * @param BlacklistResourceModel $resource
     */
    public function __construct(
        private readonly BlacklistInterfaceFactory $factory,
        private readonly BlacklistResourceModel $resource
    ) {
    }

    /**
     * @param \Comave\Catalog\Api\Data\Product\BlacklistInterface $blacklist
     * @return \Comave\Catalog\Api\Data\Product\BlacklistInterface
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     */
    public function save(BlacklistInterface $blacklist)
    {
        try {
            $this->resource->save($blacklist);
        } catch (Exception $exception) {
            throw new CouldNotSaveException(
                __($exception->getMessage())
            );
        }

        return $blacklist;
    }

    /**
     * @param int $blacklistId
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function deleteById(int $blacklistId)
    {
        return $this->delete($this->get($blacklistId));
    }

    /**
     * @param \Comave\Catalog\Api\Data\Product\BlacklistInterface $blacklist
     * @return true
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     */
    public function delete(BlacklistInterface $blacklist)
    {
        try {
            $id = $blacklist->getBlacklistId();
            $this->resource->delete($blacklist);
            unset($this->cache[$id]);
        } catch (Exception $exception) {
            throw new CouldNotDeleteException(
                __($exception->getMessage())
            );
        }

        return true;
    }

    /**
     * @param int $blacklistId
     * @return \Comave\Catalog\Api\Data\Product\BlacklistInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(int $blacklistId)
    {
        if (!isset($this->cache[$blacklistId])) {
            $orderBlacklist = $this->factory->create();
            $this->resource->load($orderBlacklist, $blacklistId);
            if (!$orderBlacklist->getId()) {
                throw new NoSuchEntityException(
                    __('The Product Blacklist with the ID "%1" does not exist . ', $blacklistId)
                );
            }
            $this->cache[$blacklistId] = $orderBlacklist;
        }

        return $this->cache[$blacklistId];
    }

    /**
     * @inheritDoc
     */
    public function clear(): void
    {
        $this->cache = [];
    }
}
