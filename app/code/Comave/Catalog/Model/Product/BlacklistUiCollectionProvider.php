<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Model\Product;

use Comave\Catalog\Api\Data\Product\BlacklistInterface;
use Comave\Catalog\Model\ResourceModel\Product\Blacklist\CollectionFactory;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Umc\Crud\Ui\CollectionProviderInterface;

class BlacklistUiCollectionProvider implements CollectionProviderInterface
{
    /**
     * @param \Comave\Catalog\Model\ResourceModel\Product\Blacklist\CollectionFactory $factory
     */
    public function __construct(private readonly CollectionFactory $factory)
    {
    }

    /**
     * @return \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
     */
    public function getCollection(): AbstractCollection
    {
        return $this->factory->create();
    }

    /**
     * @param string $productSku
     * @return array
     */
    public function getBySku(string $productSku): array
    {
        $trackingCollection = $this->getCollection();
        $trackingCollection->addFieldToFilter(BlacklistInterface::PRODUCT_SKU, ['eq' => $productSku]);

        return $trackingCollection->getItems();
    }
}
