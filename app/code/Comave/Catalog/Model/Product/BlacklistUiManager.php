<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Model\Product;

use Comave\Catalog\Api\Data\Product\BlacklistInterface;
use Comave\Catalog\Api\Product\BlacklistListRepositoryInterface;
use Comave\Catalog\Api\Product\BlacklistRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Model\AbstractModel;
use Umc\Crud\Ui\EntityUiManagerInterface;

class BlacklistUiManager implements EntityUiManagerInterface
{
    /**
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     * @param \Comave\Catalog\Api\Product\BlacklistListRepositoryInterface $listRepository
     * @param \Comave\Catalog\Api\Product\BlacklistRepositoryInterface $repository
     * @param \Comave\Catalog\Model\Product\BlacklistFactory $factory
     */
    public function __construct(
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly BlacklistListRepositoryInterface $listRepository,
        private readonly BlacklistRepositoryInterface $repository,
        private readonly BlacklistFactory $factory
    ) {
    }

    /**
     * @param \Magento\Framework\Model\AbstractModel $blacklist
     * @return void
     */
    public function save(AbstractModel $blacklist)
    {
        $this->repository->save($blacklist);
    }

    /**
     * @param int $id
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\CouldNotDeleteException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(int $id)
    {
        $this->repository->deleteById($id);
    }

    /**
     * @param int|null $id
     * @return \Magento\Framework\Model\AbstractModel | Blacklist | BlacklistInterface;
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get(?int $id)
    {
        return ($id)
            ? $this->repository->get($id)
            : $this->factory->create();
    }

    /**
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(): array
    {
        return $this->listRepository->getList($this->searchCriteriaBuilder->create())->getItems();
    }

    /**
     * @param $sku
     * @return bool
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function isBlacklisted($sku): bool
    {
        $searchCriteria = $this->searchCriteriaBuilder->addFilter(BlacklistInterface::PRODUCT_SKU, $sku)->create();

        return (bool)count($this->listRepository->getList($searchCriteria)->getItems());
    }
}
