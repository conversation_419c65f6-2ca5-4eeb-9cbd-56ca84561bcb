<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Catalog\Model\Product;

use Comave\Catalog\Api\Data\Product\BlacklistInterface;
use Comave\Catalog\Model\ResourceModel\Product\Blacklist as ProductBlacklistResourceModel;
use Magento\Framework\Api\ExtensionAttributesInterface;
use Magento\Framework\Model\AbstractExtensibleModel;

class Blacklist extends AbstractExtensibleModel implements BlacklistInterface
{
    /**
     * Cache tag
     *
     * @var string
     */
    public const string CACHE_TAG = 'comave_catalog_product_blacklist';
    /**
     * Cache tag
     *
     * @var string
     * phpcs:disable PSR2.Classes.PropertyDeclaration.Underscore,PSR12.Classes.PropertyDeclaration.Underscore
     */
    protected $_cacheTag = self::CACHE_TAG;
    /**
     * Event prefix
     *
     * @var string
     */
    protected $_eventPrefix = 'comave_catalog_product_blacklist';
    /**
     * Event object
     *
     * @var string
     */
    protected $_eventObject = 'comave_catalog_product_blacklist';
    //phpcs:enable

    /**
     * Get identities
     *
     * @return array
     */
    public function getIdentities(): array
    {
        return [sprintf("%s_%s", self::CACHE_TAG, $this->getId())];
    }

    /**
     * @return \Magento\Framework\Api\ExtensionAttributesInterface|null
     */
    public function getExtensionAttributes(): ?ExtensionAttributesInterface
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * @param \Magento\Framework\Api\ExtensionAttributesInterface $extensionAttributes
     * @return \Comave\Catalog\Model\Product\Blacklist
     */
    public function setExtensionAttributes(ExtensionAttributesInterface $extensionAttributes): Blacklist
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * Initialize resource model
     *
     * @return void
     * phpcs:disable PSR2.Methods.MethodDeclaration.Underscore, PSR12.Methods.MethodDeclaration.Underscore
     */
    protected function _construct()
    {
        $this->_init(ProductBlacklistResourceModel::class);
    }

    /**
     * @param int $id
     * @return \Comave\Catalog\Api\Data\Product\BlacklistInterface
     */
    public function setBlacklistId(int $id): BlacklistInterface
    {
        return $this->setData(self::BLACKLIST_ID, $id);
    }

    /**
     * @return int
     */
    public function getBlacklistId(): int
    {
        return (int)$this->getData(self::BLACKLIST_ID);
    }

    /**
     * @return string
     */
    public function getProductSku(): string
    {
        return (string)$this->getData(self::PRODUCT_SKU);
    }

    /**
     * @param string $productSku
     * @return self
     */
    public function setProductSku(string $productSku): BlacklistInterface
    {
        return $this->setData(self::PRODUCT_SKU, $productSku);
    }

    /**
     * @return string|null
     */
    public function getProductName(): ?string
    {
        return $this->getData(self::PRODUCT_NAME);
    }

    /**
     * @param string $productName
     * @return self
     */
    public function setProductName(?string $productName): BlacklistInterface
    {
        return $this->setData(self::PRODUCT_NAME, $productName);
    }

    /**
     * @return int
     */
    public function getProductStatus(): int
    {
        return (int)$this->getData(self::PRODUCT_STATUS);
    }

    /**
     * @param int $productStatus
     * @return self
     */
    public function setProductStatus(int $productStatus): BlacklistInterface
    {
        return $this->setData(self::PRODUCT_STATUS, $productStatus);
    }
}
