<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Marketplace\Setup\Patch\Data;

use Comave\Marketplace\Model\FixtureManager;
use Magento\Customer\Model\Customer;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;


class InstallMissingAttributesV3 implements DataPatchInterface
{
    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param EavSetupFactory $eavSetupFactory
     * @param FixtureManager $fixtureManager
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly EavSetupFactory $eavSetupFactory,
        private readonly FixtureManager $fixtureManager
    ) {
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Validator\ValidateException
     */
    public function apply(): void
    {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $attributes = $this->fixtureManager->getData('general-data');

        foreach ($attributes as $entityType => $attributesData) {
            foreach ($attributesData as $attributeData) {
                $code = $attributeData['attribute_code'];
                unset($attributeData['attribute_code']);
                $attributeData['group'] = 'Comave Settings';
                $eavSetup->removeAttribute($entityType, $code);
                $eavSetup->addAttribute($entityType, $code, $attributeData);
            }
        }
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}
