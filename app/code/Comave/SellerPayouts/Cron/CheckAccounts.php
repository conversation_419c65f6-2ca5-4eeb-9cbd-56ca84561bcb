<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\SellerPayouts\Cron;

use Comave\SellerPayouts\Helper\Data as DataHelper;
use Comave\SellerPayouts\Service\AdminNotificationServices;

/**
 * Class CheckAccounts connected stripe accounts used to pay sellers.
 */
class CheckAccounts
{

    /**
     * @param DataHelper $dataHelper
     * @param AdminNotificationServices $adminNotificationServices
     */
    public function __construct(
        private readonly DataHelper $dataHelper,
        private readonly AdminNotificationServices $adminNotificationServices,
    ) {
    }

    /**
     * @return void
     * @throws \Stripe\Exception\ApiErrorException
     */
    public function execute(): void
    {
        if (!$this->dataHelper->isEnabled()) {
            return;
        }
        $this->adminNotificationServices->stripeConnectionStatuses();
    }
}
