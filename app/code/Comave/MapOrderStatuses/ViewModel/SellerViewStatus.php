<?php
declare(strict_types=1);

namespace Comave\MapOrderStatuses\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;

class SellerViewStatus implements ArgumentInterface
{
    /**
     * @return array[]
     */
    public function getDisabledStatuses(): array
    {
        return [
            'pending_payment',
            'payment_review',
            'pending',
            'canceled_by_customer',
            'declined',
            'order_accepted',
            'canceled_by_seller',
            'delivered',
            'partial_refund',
            'refunded',
            'return_pending_approval',
            'on_hold',
            'fraud',
            'unshippable',
        ];
    }

    /**
     * @return array[]
     */
    public function getHiddenStatuses (): array
    {
        return [
            'delivered'
        ];
    }
}
