<?php
declare(strict_types=1);

namespace Comave\Customer\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use <PERSON>gento\Customer\Setup\CustomerSetupFactory;
use Magento\Customer\Model\Customer;
use Comave\Customer\Model\Attribute\Backend\UniquePhoneNo;


/**
 * Data patch that marks the customer attribute `phone_no` as unique.
 *
 */
class UniquePhoneNoAttributeV2 implements DataPatchInterface
{
    /**
     * @param ModuleDataSetupInterface $moduleDataSetup  Setup interface for DB operations
     * @param CustomerSetupFactory     $customerSetupFactory  Factory to create CustomerSetup instances
     */
    public function __construct(
        private ModuleDataSetupInterface $moduleDataSetup,
        private CustomerSetupFactory $customerSetupFactory
    ) {}

    /**
     * Apply the patch.
     *
     * Looks up the `phone_no` attribute on the Customer entity and sets
     * its `is_unique` flag so <PERSON><PERSON><PERSON> enforces unique values.
     *
     * @throws \RuntimeException If the attribute does not already exist
     * @return $this
     */
    public function apply(): self
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $customerSetup = $this->customerSetupFactory->create(
            ['setup' => $this->moduleDataSetup]
        );

        $attribute = $customerSetup->getAttribute(Customer::ENTITY, 'phone_no');
        if (!$attribute) {
            throw new \RuntimeException("'phone_no' attribute does not exist");
        }

        $customerSetup->updateAttribute(
            Customer::ENTITY,
            'phone_no',
            [
                'backend_model' => UniquePhoneNo::class,
                'is_unique' => 1
            ]
        );

        $this->moduleDataSetup->getConnection()->endSetup();

        return $this;
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies(): array
    {
        return [
            \Comave\Customer\Setup\Patch\Data\UniquePhoneNoAttribute::class
        ];
    }

    /**
     * @inheritDoc
     */
    public function getAliases(): array
    {
        return [];
    }
}
