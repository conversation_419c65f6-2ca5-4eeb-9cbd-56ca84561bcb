<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Model;

use Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface;
use Magento\Framework\Model\AbstractModel;

class ShipTableRates extends AbstractModel implements ShipTableRatesInterface
{

    /**
     * @inheritDoc
     */
    public function _construct(): void
    {
        $this->_init(\Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates::class);
    }

    /**
     * @inheritDoc
     */
    public function getShiptableratesId(): ?int
    {
        return (int)$this->getData(self::SHIPTABLERATES_ID);
    }

    /**
     * @inheritDoc
     */
    public function setShiptableratesId(int $shiptableratesId): ShipTableRatesInterface
    {
        return $this->setData(self::SHIPTABLERATES_ID, $shiptableratesId);
    }

    /**
     * @inheritDoc
     */
    public function getSellerId(): ?int
    {
        return (int)$this->getData(self::SELLER_ID);
    }

    /**
     * @inheritDoc
     */
    public function setSellerId(int $sellerId): ShipTableRatesInterface
    {
        return $this->setData(self::SELLER_ID, $sellerId);
    }

    /**
     * @inheritDoc
     */
    public function getCourier(): ?string
    {
        return $this->getData(self::COURIER_NAME);
    }

    /**
     * @inheritDoc
     */
    public function setCourier(string $courier): ShipTableRatesInterface
    {
        return $this->setData(self::COURIER_NAME, $courier);
    }

    /**
     * @inheritDoc
     */
    public function getServiceType(): ?string
    {
        return $this->getData(self::SERVICE_TYPE);
    }

    /**
     * @inheritDoc
     */
    public function setServiceType(string $serviceType): ShipTableRatesInterface
    {
        return $this->setData(self::SERVICE_TYPE, $serviceType);
    }

    /**
     * @inheritDoc
     */
    public function getCountries(): array
    {
        $countries = $this->getData(self::COUNTRIES);

        if (is_string($countries)) {
            return explode(',', $countries); // Convert CSV string to array
        }

        return is_array($countries) ? $countries : [];
    }

    /**
     * @inheritDoc
     */
    public function setCountries($countries): ShipTableRatesInterface
    {
        return $this->setData(self::COUNTRIES, implode(',', $countries));   // Store as CSV
    }

    /**
     * @inheritDoc
     */
    public function getReturnAddressId(): ?int
    {
        return (int)$this->getData(self::RETURN_ADDRESS_ID);
    }

    /**
     * @inheritDoc
     */
    public function setReturnAddressId(int $addressId): ShipTableRatesInterface
    {
        return $this->setData(self::RETURN_ADDRESS_ID, $addressId);
    }

    /**
     * @inheritDoc
     */
    public function getWeight(): ?string
    {
        return $this->getData(self::WEIGHT);
    }

    /**
     * @inheritDoc
     */
    public function setWeight(string $weight): ShipTableRatesInterface
    {
        return $this->setData(self::WEIGHT, $weight);
    }

    /**
     * @inheritDoc
     */
    public function getShippingPrice(): ?string
    {
        return $this->getData(self::SHIPPING_PRICE);
    }

    /**
     * @inheritDoc
     */
    public function setShippingPrice(string $shippingPrice): ShipTableRatesInterface
    {
        return $this->setData(self::SHIPPING_PRICE, $shippingPrice);
    }

    /**
     * @inheritDoc
     */
    public function getPackingTime(): ?int
    {
        return (int)$this->getData(self::PACKING_TIME);
    }

    /**
     * @inheritDoc
     */
    public function setPackingTime(int $delay): ShipTableRatesInterface
    {
        return $this->setData(self::PACKING_TIME, $delay);
    }

    /**
     * @inheritDoc
     */
    public function getDeliveryTime(): ?int
    {
        return (int)$this->getData(self::DELIVERY_TIME);
    }

    /**
     * @inheritDoc
     */
    public function setDeliveryTime(int $delay): ShipTableRatesInterface
    {
        return $this->setData(self::DELIVERY_TIME, $delay);
    }

    /**
     * @inheritDoc
     */
    public function getTotalLeadTime(): ?int
    {
        return (int)$this->getData(self::TOTAL_LEAD_TIME);
    }

    /**
     * @inheritDoc
     */
    public function setTotalLeadTime(int $delay): ShipTableRatesInterface
    {
        return $this->setData(self::TOTAL_LEAD_TIME, $delay);
    }

    /**
     * @inheritDoc
     */
    public function getFreeShipping(): bool
    {
        return (bool)$this->getData(self::FREE_SHIPPING);
    }

    /**
     * @inheritDoc
     */
    public function setFreeShipping(bool $freeShipping): ShipTableRatesInterface
    {
        return $this->setData(self::FREE_SHIPPING, $freeShipping);
    }

    /**
     * @inheritDoc
     */
    public function getMinOrderAmount(): ?float
    {
        $value = $this->getData(self::MIN_ORDER_AMOUNT);
        return $value !== null ? (float)$value : null;
    }

    /**
     * @inheritDoc
     */
    public function setMinOrderAmount(float $minOrderAmount): ShipTableRatesInterface
    {
        return $this->setData(self::MIN_ORDER_AMOUNT, $minOrderAmount);
    }

    /**
     * Override to Converts countries array to string
     * @param $key
     * @param $value
     * @return ShipTableRates
     */
    public function setData($key, $value = null)
    {
        // do required conversion for countries
        if (is_array($key)) {
            // Handling setData(array $data)
            if (isset($key['countries']) && is_array($key['countries'])) {
                $key['countries'] = implode(',', $key['countries']); // Convert to CSV
            }
        } elseif ($key === 'countries' && is_array($value)) {
            // Handling setData('countries', array)
            $value = implode(',', $value);
        }

        // call parent with converted values
        return parent::setData($key, $value);
    }

    /**
     * Override to Converts countries array to string
     * @param array $data
     * @return ShipTableRates
     */
    public function addData(array $data)
    {
        if (isset($data['countries']) && is_array($data['countries'])) {
            $data['countries'] = implode(',', $data['countries']); // Convert array to CSV
        }

        return parent::addData($data);
    }
}

