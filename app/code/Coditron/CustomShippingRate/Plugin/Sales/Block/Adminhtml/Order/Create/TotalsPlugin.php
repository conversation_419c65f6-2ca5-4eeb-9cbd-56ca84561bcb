<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

declare(strict_types=1);

namespace Coditron\CustomShippingRate\Plugin\Sales\Block\Adminhtml\Order\Create;

use Magento\Sales\Block\Adminhtml\Order\Create\Totals;
use Coditron\CustomShippingRate\Helper\Data as CustomShippingHelper;
use Psr\Log\LoggerInterface;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;

/**
 * Plugin to modify admin order creation totals to show seller information in shipping
 */
class TotalsPlugin
{
    /**
     * @var CustomShippingHelper
     */
    private readonly CustomShippingHelper $customShippingHelper;

    /**
     * @var LoggerInterface
     */
    private readonly LoggerInterface $logger;

    /**
     * @var MarketplaceHelper
     */
    private readonly MarketplaceHelper $marketplaceHelper;

    /**
     * @param CustomShippingHelper $customShippingHelper
     * @param LoggerInterface $logger
     * @param MarketplaceHelper $marketplaceHelper
     */
    public function __construct(
        CustomShippingHelper $customShippingHelper,
        LoggerInterface $logger,
        MarketplaceHelper $marketplaceHelper
    ) {
        $this->customShippingHelper = $customShippingHelper;
        $this->logger = $logger;
        $this->marketplaceHelper = $marketplaceHelper;
        
        // Log that the plugin was instantiated
        $this->logger->info('[AdminTotalsPlugin] ===== PLUGIN INSTANTIATED ===== Admin Totals Plugin loaded');
    }

    /**
     * Modify shipping total label to include seller information
     *
     * @param Totals $subject
     * @param array $result
     * @return array
     */
    public function afterGetTotals(Totals $subject, array $result): array
    {
        $this->logger->info('[AdminTotalsPlugin] ===== PLUGIN CALLED ===== afterGetTotals called with ' . count($result) . ' totals');

        $quote = $subject->getQuote();
        
        if (!$quote) {
            $this->logger->warning('[AdminTotalsPlugin] No quote found');
            return $result;
        }

        $this->logger->info('[AdminTotalsPlugin] Processing quote: ' . $quote->getId());

        // Find and modify the shipping total
        foreach ($result as $code => $total) {
            $this->logger->info('[AdminTotalsPlugin] Processing total: ' . $code . ' - ' . (is_object($total) ? get_class($total) : gettype($total)));
            
            if ($code === 'shipping' && $total instanceof \Magento\Framework\DataObject) {
                $originalLabel = $total->getLabel();
                $this->logger->info('[AdminTotalsPlugin] Found shipping total with label: ' . ($originalLabel ?: 'NULL'));

                // Skip if label is null or empty
                if (!$originalLabel) {
                    $this->logger->warning('[AdminTotalsPlugin] Shipping total has no label, skipping');
                    continue;
                }

                // Get seller information for this quote
                $sellerInfo = $this->getSellerInfoForQuote($quote);

                if (!empty($sellerInfo)) {
                    $newLabel = $this->createSellerShippingLabel($originalLabel, $sellerInfo);
                    $total->setLabel($newLabel);
                    $this->logger->info('[AdminTotalsPlugin] Updated shipping label to: ' . $newLabel);
                } else {
                    $this->logger->info('[AdminTotalsPlugin] No seller info found for quote');
                }
            }
        }

        return $result;
    }

    /**
     * Modify the rendered totals HTML to include seller information
     *
     * @param Totals $subject
     * @param string $result
     * @return string
     */
    public function afterRenderTotals(Totals $subject, string $result): string
    {
        $this->logger->info('[AdminTotalsPlugin] ===== RENDER TOTALS CALLED =====');
        $this->logger->info('[AdminTotalsPlugin] Original HTML length: ' . strlen($result));

        $quote = $subject->getQuote();

        if (!$quote) {
            $this->logger->warning('[AdminTotalsPlugin] No quote found in renderTotals');
            return $result;
        }

        // Get seller information for this quote
        $sellerInfo = $this->getSellerInfoForQuote($quote);

        if (empty($sellerInfo)) {
            $this->logger->info('[AdminTotalsPlugin] No seller info found in renderTotals');
            return $result;
        }

        // Log the actual HTML to see its structure
        $this->logger->info('[AdminTotalsPlugin] HTML content: ' . substr($result, 0, 500) . '...');

        // Modify the HTML to include seller information in shipping label
        $modifiedResult = $this->modifyShippingLabelInHtml($result, $sellerInfo, $quote);

        if ($modifiedResult !== $result) {
            $this->logger->info('[AdminTotalsPlugin] HTML modified successfully');
        } else {
            $this->logger->warning('[AdminTotalsPlugin] No HTML modification made');
        }

        return $modifiedResult;
    }

    /**
     * Modify shipping label in the HTML output
     *
     * @param string $html
     * @param array $sellers
     * @param \Magento\Quote\Model\Quote $quote
     * @return string
     */
    private function modifyShippingLabelInHtml(string $html, array $sellers, \Magento\Quote\Model\Quote $quote): string
    {
        if (empty($sellers)) {
            $this->logger->warning('[AdminTotalsPlugin] No sellers found, cannot modify shipping labels');
            return $html;
        }

        // Find the shipping row pattern
        $shippingRowPattern = '/(<tr[^>]*id="shipping-exclude-tax"[^>]*>.*?<td[^>]*class="[^"]*admin__total-mark[^"]*"[^>]*>\s*)(Shipping\s*&amp;\s*Handling[^<]*)(.*?<\/tr>)/is';

        if (preg_match($shippingRowPattern, $html, $matches)) {
            $this->logger->info('[AdminTotalsPlugin] Found shipping row pattern');

            $beforeLabel = $matches[1];
            $originalLabel = $matches[2];
            $afterLabel = $matches[3];

            // Get country for threshold checking
            $country = $this->getShippingCountry($quote);

            // Get seller names from the quote items (using the same method as "By Seller" section)
            $sellerNames = [];
            $items = $quote->getAllVisibleItems();
            foreach ($items as $item) {
                $productId = (int)$item->getProduct()->getId();
                $sellerInfo = $this->getSellerInfoByProductId($productId);
                if (!empty($sellerInfo['id']) && !empty($sellerInfo['name'])) {
                    $sellerNames[(int)$sellerInfo['id']] = $sellerInfo['name'];
                }
            }

            // Create new shipping rows for each seller
            $newShippingRows = '';
            foreach ($sellers as $index => $sellerId) {
                // Ensure seller ID is integer
                $sellerId = (int)$sellerId;

                // Get seller name from our lookup
                $sellerName = $sellerNames[$sellerId] ?? 'Seller ' . $sellerId;

                // Calculate seller subtotal
                $sellerSubtotal = $this->calculateSellerSubtotalFromQuote($quote, $sellerId);

                // Check if seller meets free shipping threshold
                $threshold = $this->customShippingHelper->getFreeShippingThresholdForSellerAndCountry(
                    $country,
                    $sellerSubtotal,
                    $sellerId
                );

                $isFreeShipping = ($threshold !== null);

                if ($isFreeShipping) {
                    $tooltipText = 'Free shipping applied! Order subtotal €' . number_format($sellerSubtotal, 2) . ' meets the minimum threshold of €' . number_format($threshold, 2) . ' for ' . $country;
                    $infoIcon = '<span title="' . htmlspecialchars($tooltipText) . '"
                                      data-tooltip-delay="0"
                                      style="margin: 0 4px;
                                             display: inline-block;
                                             width: 12px;
                                             height: 12px;
                                             border: 1px solid #666;
                                             color: #666;
                                             border-radius: 2px;
                                             font-size: 8px;
                                             font-weight: bold;
                                             cursor: help;
                                             text-align: center;
                                             line-height: 10px;
                                             vertical-align: baseline;
                                             position: relative;
                                             top: -1px;">!</span>';

                    $sellerLabel = 'Shipping &amp; Handling (' . $sellerName . ') - Free' . $infoIcon;
                    $priceHtml = '<span class="price">€0.00</span>';
                    $this->logger->info('[AdminTotalsPlugin] Seller ' . $sellerId . ' (' . $sellerName . ') qualifies for free shipping (threshold: ' . $threshold . ', subtotal: ' . $sellerSubtotal . ')');
                } else {
                    $sellerLabel = 'Shipping &amp; Handling (' . $sellerName . ')';
                    $priceHtml = '<span class="price">€20.00</span>'; // Keep original price
                    $this->logger->info('[AdminTotalsPlugin] Seller ' . $sellerId . ' (' . $sellerName . ') does not qualify for free shipping (subtotal: ' . $sellerSubtotal . ')');
                }

                $rowId = 'shipping-exclude-tax-seller-' . $sellerId;

                // Replace both label and price
                $newRow = str_replace(
                    ['id="shipping-exclude-tax"', $originalLabel],
                    ['id="' . $rowId . '"', $sellerLabel],
                    $matches[0]
                );

                // Replace the price if it's free shipping
                if ($isFreeShipping) {
                    $newRow = preg_replace('/<span class="price">[^<]*<\/span>/', $priceHtml, $newRow);
                }

                $newShippingRows .= $newRow;

                $this->logger->info('[AdminTotalsPlugin] Created shipping row for seller: ' . $sellerId . ' - ' . ($isFreeShipping ? 'FREE' : 'PAID'));
            }

            // Replace the original shipping row with multiple seller rows
            $html = str_replace($matches[0], $newShippingRows, $html);
            $this->logger->info('[AdminTotalsPlugin] Successfully replaced shipping row with ' . count($sellers) . ' seller rows');

        } else {
            $this->logger->warning('[AdminTotalsPlugin] Could not find shipping row pattern');

            // Fallback: simple text replacement for first seller
            $firstSellerId = $sellers[0];
            $newShippingLabel = 'Shipping1 &amp; Handling1 - Seller' . $firstSellerId;

            $testHtml = str_replace('Shipping &amp; Handling', $newShippingLabel, $html);
            if ($testHtml !== $html) {
                $this->logger->info('[AdminTotalsPlugin] Fallback: Successfully changed Shipping & Handling to: ' . $newShippingLabel);
                $html = $testHtml;
            }
        }

        // Pattern to match shipping label in the HTML
        $pattern = '/(<td[^>]*class="[^"]*admin__total-mark[^"]*"[^>]*>\s*)(Shipping\s*&\s*Handling[^<]*)/i';

        // Get seller names from our lookup (we already collected them above)
        $displayNames = [];
        foreach ($sellers as $sellerId) {
            $displayNames[] = $sellerNames[$sellerId] ?? 'Seller ' . $sellerId;
        }
        $sellerText = count($displayNames) === 1 ? $displayNames[0] : implode(', ', $displayNames);

        $replacement = '$1$2 (' . $sellerText . ')';

        $modifiedHtml = preg_replace($pattern, $replacement, $html);

        $this->logger->info('[AdminTotalsPlugin] Regex pattern: ' . $pattern);
        $this->logger->info('[AdminTotalsPlugin] Replacement: ' . $replacement);
        $this->logger->info('[AdminTotalsPlugin] Match found: ' . (($modifiedHtml !== $html) ? 'YES' : 'NO'));

        return $modifiedHtml;
    }

    /**
     * Get seller information for the quote
     *
     * @param \Magento\Quote\Model\Quote $quote
     * @return array
     */
    private function getSellerInfoForQuote(\Magento\Quote\Model\Quote $quote): array
    {
        $sellers = [];
        $items = $quote->getAllVisibleItems();

        $this->logger->info('[AdminTotalsPlugin] Quote has ' . count($items) . ' visible items');

        foreach ($items as $item) {
            $this->logger->info('[AdminTotalsPlugin] Processing item: ' . $item->getId() . ' - ' . $item->getName());

            // Get seller info using product ID (same as the working "By Seller" section)
            $productId = (int)$item->getProduct()->getId();
            $sellerInfo = $this->getSellerInfoByProductId($productId);

            if (!empty($sellerInfo['id']) && !in_array((int)$sellerInfo['id'], $sellers)) {
                $sellers[] = (int)$sellerInfo['id'];
                $this->logger->info('[AdminTotalsPlugin] Found seller: ' . $sellerInfo['id'] . ' (' . $sellerInfo['name'] . ') for product: ' . $productId);
            }
        }

        $this->logger->info('[AdminTotalsPlugin] Found sellers: ' . (empty($sellers) ? 'NONE' : implode(', ', $sellers)));

        return $sellers;
    }



    /**
     * Create shipping label with seller information
     *
     * @param string|null $originalLabel
     * @param array $sellers
     * @return string
     */
    private function createSellerShippingLabel(?string $originalLabel, array $sellers): string
    {
        if (!$originalLabel || empty($sellers)) {
            return $originalLabel ?: 'Shipping & Handling';
        }

        if (count($sellers) === 1) {
            $sellerName = $this->getSellerName($sellers[0]);
            return $originalLabel . ' (' . $sellerName . ')';
        }

        // Multiple sellers - show count
        $sellerNames = array_map([$this, 'getSellerName'], $sellers);
        return $originalLabel . ' (' . implode(', ', $sellerNames) . ')';
    }

    /**
     * Get shipping country from quote
     *
     * @param \Magento\Quote\Model\Quote|null $quote
     * @return string
     */
    private function getShippingCountry(?\Magento\Quote\Model\Quote $quote): string
    {
        if (!$quote) {
            return '';
        }

        $shippingAddress = $quote->getShippingAddress();
        $countryId = $shippingAddress ? $shippingAddress->getCountryId() : '';

        $this->logger->info('[AdminTotalsPlugin] Shipping country: ' . ($countryId ?: 'EMPTY'));

        // Ensure we always return a string
        return (string)($countryId ?: '');
    }

    /**
     * Calculate subtotal for a specific seller from quote
     *
     * @param \Magento\Quote\Model\Quote|null $quote
     * @param int $sellerId
     * @return float
     */
    private function calculateSellerSubtotalFromQuote(?\Magento\Quote\Model\Quote $quote, int $sellerId): float
    {
        if (!$quote) {
            return 0;
        }

        $subtotal = 0;
        $items = $quote->getAllVisibleItems();

        foreach ($items as $item) {
            // Get seller info by product ID (same as "Items Ordered" section)
            $productId = (int)$item->getProduct()->getId();
            $sellerInfo = $this->getSellerInfoByProductId($productId);
            $itemSellerId = !empty($sellerInfo['id']) ? (int)$sellerInfo['id'] : null;

            if ($itemSellerId === $sellerId) {
                $subtotal += $item->getBaseRowTotal() - $item->getBaseDiscountAmount();
            }
        }

        $this->logger->info('[AdminTotalsPlugin] Calculated subtotal for seller ' . $sellerId . ': ' . $subtotal);
        return $subtotal;
    }

    /**
     * Get seller info by product ID using direct marketplace helper
     *
     * @param int $productId
     * @return array
     */
    private function getSellerInfoByProductId(int $productId): array
    {
        try {
            $this->logger->info('[AdminTotalsPlugin] Getting seller info for product ID: ' . $productId);

            // Get seller ID directly from marketplace helper
            $sellerId = $this->marketplaceHelper->getSellerIdByProductId($productId);

            if (!$sellerId) {
                $this->logger->info('[AdminTotalsPlugin] No seller found for product: ' . $productId);
                return [];
            }

            // Get seller details
            $sellerInfo = $this->marketplaceHelper->getSellerInfo($sellerId);
            $this->logger->info('[AdminTotalsPlugin] Seller info for ID ' . $sellerId . ': ' . json_encode($sellerInfo));

            if (!empty($sellerInfo)) {
                // Try different name fields from seller info
                $name = $sellerInfo['shop_title'] ??
                       $sellerInfo['company_locality'] ??
                       'Seller ' . $sellerId;

                return [
                    'id' => $sellerId,
                    'name' => $name
                ];
            }

        } catch (\Exception $e) {
            $this->logger->error('[AdminTotalsPlugin] Error getting seller info for product ' . $productId . ': ' . $e->getMessage());
        }

        return [];
    }


}
