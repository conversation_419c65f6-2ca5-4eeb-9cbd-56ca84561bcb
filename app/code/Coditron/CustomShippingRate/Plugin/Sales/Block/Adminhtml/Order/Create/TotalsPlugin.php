<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

declare(strict_types=1);

namespace Coditron\CustomShippingRate\Plugin\Sales\Block\Adminhtml\Order\Create;

use Magento\Sales\Block\Adminhtml\Order\Create\Totals;
use Coditron\CustomShippingRate\Helper\Data as CustomShippingHelper;
use Webkul\Marketplace\Service\UserInfoService;
use Magento\Framework\Pricing\PriceCurrencyInterface;

/**
 * Plugin to modify admin order creation totals to show seller-specific shipping information
 */
class TotalsPlugin
{
    public function __construct(
        private readonly CustomShippingHelper $customShippingHelper,
        private readonly UserInfoService $userInfoService,
        private readonly PriceCurrencyInterface $priceCurrency
    ) {}

    /**
     * Modify the rendered totals HTML to show seller-specific shipping information
     *
     * @param Totals $subject
     * @param string $result
     * @return string
     */
    public function afterRenderTotals(Totals $subject, string $result): string
    {
        $quote = $subject->getQuote();
        if (!$quote) {
            return $result;
        }

        $sellers = $this->getSellerIdsFromQuote($quote);
        if (empty($sellers)) {
            return $result;
        }

        return $this->modifyShippingRowsInHtml($result, $sellers, $quote);
    }

    /**
     * Get seller IDs from quote items
     *
     * @param \Magento\Quote\Model\Quote $quote
     * @return array
     */
    private function getSellerIdsFromQuote(\Magento\Quote\Model\Quote $quote): array
    {
        $sellers = [];
        $items = $quote->getAllVisibleItems();

        foreach ($items as $item) {
            $productId = (int)$item->getProduct()->getId();
            $userInfo = $this->userInfoService->get($productId);

            if (!empty($userInfo['id']) && !in_array((int)$userInfo['id'], $sellers)) {
                $sellers[] = (int)$userInfo['id'];
            }
        }

        return $sellers;
    }

    /**
     * Modify shipping rows in HTML to show seller-specific information
     *
     * @param string $html
     * @param array $sellers
     * @param \Magento\Quote\Model\Quote $quote
     * @return string
     */
    private function modifyShippingRowsInHtml(string $html, array $sellers, \Magento\Quote\Model\Quote $quote): string
    {
        $shippingRowPattern = '/(<tr[^>]*id="shipping-exclude-tax"[^>]*>.*?<td[^>]*class="[^"]*admin__total-mark[^"]*"[^>]*>\s*)(Shipping\s*&amp;\s*Handling[^<]*)(.*?<\/tr>)/is';

        if (!preg_match($shippingRowPattern, $html, $matches)) {
            return $html;
        }

        $country = $this->getShippingCountry($quote);
        $sellerNames = $this->getSellerNamesFromQuote($quote);
        $shippingAmount = $this->getShippingAmount($quote);
        $newShippingRows = '';

        foreach ($sellers as $sellerId) {
            $sellerId = (int)$sellerId;
            $sellerName = $sellerNames[$sellerId] ?? 'Seller ' . $sellerId;
            $sellerSubtotal = $this->calculateSellerSubtotal($quote, $sellerId);

            $threshold = $this->customShippingHelper->getFreeShippingThresholdForSellerAndCountry(
                $country,
                $sellerSubtotal,
                $sellerId
            );

            $isFreeShipping = ($threshold !== null);

            if ($isFreeShipping) {
                $formattedThreshold = $this->formatPrice($threshold, $quote);
                $formattedSubtotal = $this->formatPrice($sellerSubtotal, $quote);
                // Extract plain text for tooltip with bold Unicode formatting for prices
                $plainSubtotal = strip_tags($formattedSubtotal);
                $plainThreshold = strip_tags($formattedThreshold);
                $boldSubtotal = $this->makeBoldUnicode($plainSubtotal);
                $boldThreshold = $this->makeBoldUnicode($plainThreshold);
                $tooltipText = 'Free shipping applied! Order subtotal ' . $boldSubtotal .
                              ' meets the minimum threshold ' . $boldThreshold . ' for ' . $country;
                $infoIcon = $this->createInfoIcon($tooltipText);
                $sellerLabel = 'Shipping &amp; Handling (' . $sellerName . ') - Free' . $infoIcon;
                $priceHtml = '<span class="price">' . $this->formatPrice(0, $quote) . '</span>';
            } else {
                $sellerLabel = 'Shipping &amp; Handling (' . $sellerName . ')';
                $priceHtml = '<span class="price">' . $this->formatPrice($shippingAmount, $quote) . '</span>';
            }

            $rowId = 'shipping-exclude-tax-seller-' . $sellerId;
            $newRow = str_replace(
                ['id="shipping-exclude-tax"', $matches[2]],
                ['id="' . $rowId . '"', $sellerLabel],
                $matches[0]
            );

            // Always replace the price with the correct amount (free or regular)
            $newRow = preg_replace('/<span class="price">[^<]*<\/span>/', $priceHtml, $newRow);

            $newShippingRows .= $newRow;
        }

        return str_replace($matches[0], $newShippingRows, $html);
    }

    /**
     * Get seller names mapped by seller ID from quote items
     *
     * @param \Magento\Quote\Model\Quote $quote
     * @return array
     */
    private function getSellerNamesFromQuote(\Magento\Quote\Model\Quote $quote): array
    {
        $sellerNames = [];
        $items = $quote->getAllVisibleItems();

        foreach ($items as $item) {
            $productId = (int)$item->getProduct()->getId();
            $userInfo = $this->userInfoService->get($productId);

            if (!empty($userInfo['id']) && !empty($userInfo['name'])) {
                $sellerNames[(int)$userInfo['id']] = $userInfo['name'];
            }
        }

        return $sellerNames;
    }

    /**
     * Get shipping country from quote
     *
     * @param \Magento\Quote\Model\Quote $quote
     * @return string
     */
    private function getShippingCountry(\Magento\Quote\Model\Quote $quote): string
    {
        $shippingAddress = $quote->getShippingAddress();
        return (string)($shippingAddress ? $shippingAddress->getCountryId() : '');
    }

    /**
     * Calculate subtotal for a specific seller from quote
     *
     * @param \Magento\Quote\Model\Quote $quote
     * @param int $sellerId
     * @return float
     */
    private function calculateSellerSubtotal(\Magento\Quote\Model\Quote $quote, int $sellerId): float
    {
        $subtotal = 0;
        $items = $quote->getAllVisibleItems();

        foreach ($items as $item) {
            $productId = (int)$item->getProduct()->getId();
            $userInfo = $this->userInfoService->get($productId);
            $itemSellerId = !empty($userInfo['id']) ? (int)$userInfo['id'] : null;

            if ($itemSellerId === $sellerId) {
                $subtotal += $item->getBaseRowTotal() - $item->getBaseDiscountAmount();
            }
        }

        return $subtotal;
    }

    /**
     * Get shipping amount from quote
     *
     * @param \Magento\Quote\Model\Quote $quote
     * @return float
     */
    private function getShippingAmount(\Magento\Quote\Model\Quote $quote): float
    {
        $shippingAddress = $quote->getShippingAddress();
        if (!$shippingAddress) {
            return 0.0;
        }

        // Get shipping amount from totals
        $totals = $shippingAddress->getTotals();
        if (isset($totals['shipping']) && $totals['shipping']->getValue()) {
            return (float)$totals['shipping']->getValue();
        }

        // Fallback to shipping amount from address
        return (float)$shippingAddress->getShippingAmount();
    }

    /**
     * Format price using quote currency
     *
     * @param float $amount
     * @param \Magento\Quote\Model\Quote $quote
     * @return string
     */
    private function formatPrice(float $amount, \Magento\Quote\Model\Quote $quote): string
    {
        return $this->priceCurrency->format(
            $amount,
            true,
            PriceCurrencyInterface::DEFAULT_PRECISION,
            $quote->getStore(),
            $quote->getQuoteCurrencyCode()
        );
    }

    /**
     * Create info icon with tooltip for free shipping
     *
     * @param string $tooltipText
     * @return string
     */
    private function createInfoIcon(string $tooltipText): string
    {
        return '<span title="' . htmlspecialchars($tooltipText) . '"
                      style="margin: 0 4px;
                             display: inline-block;
                             width: 12px;
                             height: 12px;
                             border: 1px solid #666;
                             color: #666;
                             border-radius: 2px;
                             font-size: 8px;
                             font-weight: bold;
                             cursor: help;
                             text-align: center;
                             line-height: 10px;
                             vertical-align: baseline;
                             position: relative;
                             top: -1px;">!</span>';
    }
}
