"<legend class=\"admin__legend\"><span>Order Totals</span></legend>\n<br>\n\n<table class=\"admin__table-secondary data-table\">\n    <tbody>\n        <tr id=\"subtotal-total\" class=\"row-totals\">\n    <td class=\"admin__total-mark\" colspan=\"1\">\n        Subtotal    </td>\n    <td class=\"admin__total-amount\">\n        <span class=\"price\">$40.00</span>    </td>\n</tr>\n    <tr id=\"shipping-exclude-tax-seller-67440\" class=\"row-totals\">\n    <td class=\"admin__total-mark\" colspan=\"1\">\n        Shipping &amp; Handling (aymane B)</td>\n    <td class=\"admin__total-amount\">\n        <span class=\"price\"><span class=\"price\">$0.00</span>.00</span>    </td>\n</tr>\n    \n            <tr id=\"grand-total\" class=\"row-totals\">\n        <td class=\"admin__total-mark\" colspan=\"1\">\n            <strong>Grand Total</strong>\n        </td>\n        <td class=\"admin__total-amount\">\n            <strong><span class=\"price\">$40.00</span></strong>\n        </td>\n    </tr>\n            </tbody>\n</table>\n\n<div class=\"order-totals-actions\">\n    <div class=\"admin__field admin__field-option field-append-comments\">\n        <input type=\"checkbox\" id=\"notify_customer\" name=\"order[comment][customer_note_notify]\"\n               value=\"1\" checked=\"checked\"               class=\"admin__control-checkbox\"/>\n        <label for=\"notify_customer\" class=\"admin__field-label\">\n            Append Comments        </label>\n    </div>\n        <div class=\"admin__field admin__field-option field-email-order-confirmation\">\n        <input type=\"checkbox\" id=\"send_confirmation\" name=\"order[send_confirmation]\" value=\"1\" checked=\"checked\"\n               class=\"admin__control-checkbox\"/>\n        <label for=\"send_confirmation\" class=\"admin__field-label\">\n            Email Order Confirmation        </label>\n    </div>\n        <div class=\"actions\">\n        <button id=\"id_Ihr48r8XKBhII16tJpbG6N7ZeCJbOq7Q\" title=\"Submit Order\" type=\"button\" class=\"action-default scalable save primary\" backend-button-widget-hook-id=\"buttonIdghszAVq5gj\"  data-ui-id=\"widget-button-0\" >\n    <span>Submit Order</span>\n</button>\n<script type=\"text&#x2F;javascript\">    function eventListeneroTG5qRxTmJ () {\n        order.submit();\n    }\n    var listenedElementoTG5qRxTmJArray = document.querySelectorAll(\"*[backend-button-widget-hook-id='buttonIdghszAVq5gj']\");\n    if(listenedElementoTG5qRxTmJArray.length !== 'undefined'){\n        listenedElementoTG5qRxTmJArray.forEach(function(element) {\n            if (element) {\n                element.onclick = function (event) {\n                    var targetElement = element;\n                    if (event && event.target) {\n                        targetElement = event.target;\n                    }\n                    return eventListeneroTG5qRxTmJ.apply(targetElement);\n                };\n            }\n        });\n    }</script>    </div>\n</div>\n\n<script>require(['prototype'], function(){\n\n//<![CDATA[\nvar sendEmailCheckbox = $('send_confirmation');\nif (sendEmailCheckbox) {\n    Event.observe(sendEmailCheckbox, 'change', notifyCustomerUpdate);\n    notifyCustomerUpdate();\n}\n\nfunction notifyCustomerUpdate() {\n    var sendEmailCheckbox = $('send_confirmation');\n    var notifyCustomerCheckbox = $('notify_customer');\n    if (!sendEmailCheckbox || !notifyCustomerCheckbox)\n        return;\n    notifyCustomerCheckbox.disabled = !sendEmailCheckbox.checked;\n}\n//]]>\n\nwindow.notifyCustomerUpdate = notifyCustomerUpdate;\nwindow.sendEmailCheckbox = sendEmailCheckbox;\n\n});</script>"